# 引用需求标准功能说明

## 功能概述

在技术要求配置中新增了"引用需求标准"功能，用户可以从政府采购需求标准库中选择和引用标准化的技术要求，提高配置效率和规范性。

## 功能特点

### 1. 大弹窗界面设计
- **弹窗尺寸**：1200px宽度，600px高度
- **响应式布局**：适配不同屏幕尺寸
- **分区域设计**：搜索区、目录区、内容区、操作区

### 2. 顶部搜索功能
支持多维度搜索，快速定位所需标准：
- **需求标准名称**：支持模糊搜索
- **采购目录**：下拉选择特定分类
- **指标名称**：精确匹配指标
- **操作按钮**：重置和搜索功能

### 3. 左侧采购目录树
- **分类展示**：按采购目录分类组织
- **目录信息**：显示名称、年版、编码
- **选择状态**：显示已选择的标准数量
- **交互反馈**：点击切换，高亮当前选中

### 4. 右侧详细数据表格
- **表格结构**：
  - 复选框：支持多选
  - 指标名称：一级/二级指标
  - 指标要求：具体技术要求
  - 使用说明：应用指导
  - 其他属性：标签形式展示
- **批量操作**：全选当页、清空选择
- **数据统计**：显示总条数

### 5. 多选引用功能
- **跨分类选择**：支持从不同采购目录选择标准
- **选择状态保持**：切换分类时保持已选状态
- **实时统计**：底部显示已选择数量
- **批量引用**：一次性引用多条标准

## 数据结构

### 采购目录分类
```javascript
{
  id: 'database',
  name: '数据库政府采购需求标准',
  year: '(2023年版)',
  code: 'A010101C1数据库',
  count: 15
}
```

### 需求标准数据
```javascript
{
  id: 'db_001',
  primaryIndicator: '功能要求',
  secondaryIndicator: '共50条',
  requirement: '操作系统应支持ARM、LoongArch、MIPS、SW64、x86等平台架构CPU',
  usageDescription: '采用人工智能监控需要提供具体需求条件',
  otherAttributes: ['必须符合安全性要求', '必须满足人员需求', '可以作为评分因素']
}
```

## 使用流程

### 1. 打开引用需求标准弹窗
1. 在技术要求编辑抽屉中
2. 点击"引用需求标准"按钮
3. 弹窗打开，默认显示数据库分类

### 2. 搜索和筛选标准
1. **按名称搜索**：在"需求标准名称"输入框输入关键词
2. **按分类筛选**：在"采购目录"下拉框选择特定分类
3. **按指标搜索**：在"指标名称"输入框输入指标关键词
4. 点击"搜索"按钮执行搜索
5. 点击"重置"按钮清空搜索条件

### 3. 浏览和选择标准
1. **切换分类**：点击左侧采购目录项切换分类
2. **查看详情**：在右侧表格中查看标准详细信息
3. **单选标准**：勾选表格中的复选框
4. **批量选择**：
   - 点击"全选当页"选择当前页面所有标准
   - 点击"清空选择"取消所有选择

### 4. 引用标准到技术要求
1. 确认已选择需要的标准（底部显示选择数量）
2. 点击"引用"按钮
3. 系统自动将选择的标准添加到技术要求列表
4. 弹窗关闭，返回技术要求编辑界面

## 技术实现

### 前端组件
- **Vue 3 Composition API**：响应式状态管理
- **Element Plus Dialog**：弹窗组件
- **Element Plus Table**：数据表格
- **Element Plus Form**：搜索表单

### 状态管理
- `referenceStandardDialogVisible`：弹窗显示状态
- `selectedCategoryId`：当前选中的分类ID
- `selectedStandards`：已选择的标准列表
- `standardSearchForm`：搜索表单数据

### 数据管理
- `procurementCategories`：采购目录分类数据
- `standardsData`：按分类组织的标准数据
- `currentStandardList`：当前显示的标准列表（计算属性）

## 界面样式

### 布局设计
- **搜索区域**：浅灰背景，水平排列搜索项
- **主内容区**：左右分栏布局
- **左侧目录**：固定宽度280px，卡片式分类项
- **右侧内容**：自适应宽度，表格展示
- **底部操作**：固定底部，左右分布信息和按钮

### 交互效果
- **悬停效果**：分类项和按钮的悬停状态
- **选中状态**：当前分类的高亮显示
- **加载状态**：搜索和操作的反馈提示

## 扩展功能

### 已实现
- ✅ 多分类标准浏览
- ✅ 多维度搜索筛选
- ✅ 多选批量引用
- ✅ 实时状态反馈

### 待扩展
- 🔄 搜索结果高亮
- 🔄 标准详情预览
- 🔄 收藏常用标准
- 🔄 标准版本管理
- 🔄 自定义标准导入

## 优势特点

1. **标准化**：引用官方标准，确保技术要求的规范性
2. **高效性**：快速选择，避免重复编写相同要求
3. **准确性**：减少人工输入错误，提高配置质量
4. **便捷性**：多维度搜索，快速定位所需标准
5. **灵活性**：支持跨分类多选，满足复杂需求

## 注意事项

1. **网络依赖**：需要稳定的网络连接获取标准数据
2. **数据更新**：定期更新标准库以保持最新状态
3. **权限控制**：根据用户权限控制可访问的标准范围
4. **性能优化**：大量数据时考虑分页和虚拟滚动
5. **兼容性**：确保在不同浏览器中的一致性表现
