# 技术要求新功能测试步骤

## 测试环境
- 浏览器：Chrome/Firefox/Safari
- 项目地址：http://localhost:3002/
- 测试页面：采购需求 -> 标项1 -> 技术要求

## 测试步骤

### 1. 进入技术要求配置页面
1. 打开浏览器访问 http://localhost:3002/
2. 点击"采购需求"标签页
3. 在标项1中找到"技术要求"卡片
4. 点击"编辑配置"按钮打开技术要求编辑抽屉

### 2. 测试"对表国标要求"功能

#### 2.1 打开对表国标要求弹窗
1. 在技术要求表格中找到任意一行
2. 在操作列中点击"对表国标要求"按钮
3. 验证弹窗是否正常打开

#### 2.2 验证弹窗内容
1. 检查"当前配置要求"区域是否显示正确信息：
   - 分类
   - 指标名称
   - 指标要求
2. 检查国标对比表格是否显示：
   - 国标编号
   - 国标名称
   - 国标要求
   - 差异分析（带颜色标签）
   - 建议

#### 2.3 测试功能操作
1. 点击"应用国标要求"按钮
2. 验证是否显示成功提示
3. 点击"关闭"按钮
4. 验证弹窗是否正常关闭

### 3. 测试"属性值参考"功能

#### 3.1 打开属性值参考弹窗
1. 在技术要求表格中找到任意一行
2. 在操作列中点击"属性值参考"按钮
3. 验证弹窗是否正常打开

#### 3.2 验证弹窗内容
1. 检查"当前指标信息"区域是否显示正确信息：
   - 指标名称
   - 当前要求
2. 检查标签页是否正常显示：
   - 常用参数值
   - 行业标准值
   - 历史配置

#### 3.3 测试常用参数值标签页
1. 点击"常用参数值"标签页
2. 验证是否显示分类和标签：
   - CPU性能等级
   - 内存规格
   - 网络性能
3. 点击任意一个标签
4. 验证是否显示选择成功提示

#### 3.4 测试行业标准值标签页
1. 点击"行业标准值"标签页
2. 验证表格是否显示：
   - 行业
   - 标准值
   - 说明
   - 操作按钮
3. 点击任意一行的"选择"按钮
4. 验证是否显示选择成功提示

#### 3.5 测试历史配置标签页
1. 点击"历史配置"标签页
2. 验证表格是否显示：
   - 项目名称
   - 配置值
   - 配置时间
   - 操作按钮
3. 点击任意一行的"选择"按钮
4. 验证是否显示选择成功提示

#### 3.6 测试应用功能
1. 选择任意一个属性值
2. 点击"应用选择的值"按钮
3. 验证是否显示应用成功提示
4. 关闭弹窗
5. 检查技术要求表格中的"指标要求"字段是否已更新

### 4. 测试界面响应性

#### 4.1 测试按钮布局
1. 验证操作列中三个按钮的布局：
   - "对表国标要求"
   - "属性值参考"
   - 删除按钮（红色）
2. 检查按钮是否垂直排列且间距合适

#### 4.2 测试弹窗样式
1. 验证弹窗宽度是否合适：
   - 对表国标要求：1000px
   - 属性值参考：800px
2. 检查弹窗内容是否完整显示
3. 验证表格和标签的样式是否正常

### 5. 测试错误处理

#### 5.1 测试空值处理
1. 在属性值参考弹窗中不选择任何值
2. 直接点击"应用选择的值"按钮
3. 验证是否显示警告提示："请先选择一个属性值"

#### 5.2 测试弹窗关闭
1. 点击弹窗右上角的关闭按钮
2. 点击弹窗外部区域
3. 按ESC键
4. 验证弹窗是否都能正常关闭

## 预期结果

### 功能正常标准
1. 所有按钮点击响应正常
2. 弹窗打开和关闭流畅
3. 数据显示完整准确
4. 操作反馈及时明确
5. 样式美观一致

### 成功标志
- ✅ 对表国标要求弹窗正常显示国标对比信息
- ✅ 属性值参考弹窗三个标签页内容完整
- ✅ 选择属性值后能成功应用到技术要求中
- ✅ 所有操作都有适当的用户反馈
- ✅ 界面布局美观，用户体验良好

## 问题记录

如果在测试过程中发现问题，请记录：
1. 问题描述
2. 复现步骤
3. 预期行为
4. 实际行为
5. 浏览器和版本信息

## 注意事项

1. 确保在测试前项目已正常启动
2. 建议使用最新版本的现代浏览器
3. 如果遇到样式问题，请检查浏览器缩放比例
4. 测试时注意观察控制台是否有错误信息
