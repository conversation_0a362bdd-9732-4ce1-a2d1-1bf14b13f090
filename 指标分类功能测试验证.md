# 指标分类功能测试验证

## 测试环境
- 项目地址：http://localhost:3002/
- 测试页面：采购需求 -> 标项1 -> 技术要求 -> 引用需求标准
- 浏览器：Chrome/Firefox/Safari 最新版本

## 快速验证步骤

### 1. 打开引用需求标准弹窗
1. 访问 http://localhost:3002/
2. 点击"采购需求"标签页
3. 在标项1中点击"技术要求"的"编辑配置"
4. 点击"引用需求标准"按钮

### 2. 验证指标分类列显示
检查表格是否包含以下列（从左到右）：
- ✅ 复选框列
- ✅ **指标分类列**（新增）
- ✅ 指标名称列
- ✅ 指标要求列
- ✅ 使用说明列
- ✅ 其他属性列

### 3. 验证分类标签颜色
在默认的"数据库政府采购需求标准"分类中，应该看到：

| 序号 | 指标分类 | 标签颜色 | 验证状态 |
|------|---------|---------|---------|
| 1 | 功能要求 | 蓝色 | ⬜ |
| 2 | 系统要求 | 灰色 | ⬜ |
| 3 | 系统要求 | 灰色 | ⬜ |
| 4 | 性能要求 | 绿色 | ⬜ |
| 5 | 安全要求 | 红色 | ⬜ |

### 4. 验证其他分类数据
切换到不同的采购目录，验证分类显示：

#### 操作系统政府采购需求标准
- 系统架构（灰色）
- 安全特性（红色）
- 性能要求（绿色）

#### 网络系统政府采购需求标准
- 性能要求（绿色）
- 功能要求（蓝色）

#### 安全系统政府采购需求标准
- 安全要求（红色）
- 安全要求（红色）

### 5. 验证表格布局
检查以下布局要素：
- ✅ 指标分类列宽度为100px
- ✅ 标签居中对齐
- ✅ 标签尺寸为small
- ✅ 表格整体布局协调

### 6. 验证功能完整性
确认新增分类列不影响原有功能：
- ✅ 复选框选择正常
- ✅ 全选/清空功能正常
- ✅ 跨分类选择正常
- ✅ 引用功能正常
- ✅ 搜索功能正常

## 详细测试用例

### 测试用例1：分类标签显示
**目标**：验证所有分类标签正确显示

**步骤**：
1. 打开引用需求标准弹窗
2. 逐一切换所有采购目录分类
3. 检查每个标准的分类标签

**预期结果**：
- 所有标准都显示分类标签
- 标签文字清晰可读
- 标签颜色符合映射规则

### 测试用例2：颜色映射验证
**目标**：验证分类颜色映射正确

**步骤**：
1. 在数据库分类中找到"功能要求"标签
2. 验证标签颜色为蓝色
3. 找到"安全要求"标签
4. 验证标签颜色为红色
5. 重复验证其他分类

**预期结果**：
- 功能要求：蓝色
- 性能要求：绿色
- 安全要求：红色
- 系统要求：灰色

### 测试用例3：表格响应性
**目标**：验证表格在不同窗口大小下的表现

**步骤**：
1. 调整浏览器窗口大小
2. 观察表格布局变化
3. 检查分类列是否保持固定宽度

**预期结果**：
- 分类列宽度保持100px
- 表格出现水平滚动条时布局正常
- 标签显示不受影响

### 测试用例4：数据完整性
**目标**：验证所有标准数据都有分类信息

**步骤**：
1. 遍历所有采购目录分类
2. 检查每个标准是否都有分类标签
3. 验证分类名称是否合理

**预期结果**：
- 无空白分类标签
- 分类名称有意义
- 数据结构完整

## 问题排查指南

### 常见问题及解决方案

#### 问题1：分类标签不显示
**可能原因**：
- 数据中缺少 `indicatorCategory` 字段
- 方法 `getIndicatorCategoryType` 未正确调用

**排查步骤**：
1. 检查浏览器控制台是否有错误
2. 验证数据结构是否正确
3. 确认方法是否正确导出

#### 问题2：标签颜色不正确
**可能原因**：
- 颜色映射规则错误
- CSS样式未正确加载

**排查步骤**：
1. 检查 `getIndicatorCategoryType` 方法返回值
2. 验证CSS样式是否生效
3. 确认Element Plus版本兼容性

#### 问题3：表格布局异常
**可能原因**：
- 列宽设置不当
- CSS样式冲突

**排查步骤**：
1. 检查表格列配置
2. 验证CSS样式优先级
3. 测试不同浏览器兼容性

## 性能验证

### 渲染性能
- 大量数据下标签渲染速度
- 表格滚动流畅性
- 内存使用情况

### 交互性能
- 分类切换响应速度
- 标签点击反应时间
- 整体用户体验

## 验证清单

### 基础功能 ✅
- [ ] 分类列正确显示
- [ ] 标签颜色映射正确
- [ ] 表格布局合理
- [ ] 数据完整性良好

### 交互功能 ✅
- [ ] 原有功能不受影响
- [ ] 选择功能正常
- [ ] 引用功能正常
- [ ] 搜索功能正常

### 视觉效果 ✅
- [ ] 标签样式美观
- [ ] 颜色搭配合理
- [ ] 文字清晰可读
- [ ] 整体协调统一

### 兼容性 ✅
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] 响应式布局正常

## 验证结果记录

### 测试日期
- 测试时间：[填写测试时间]
- 测试人员：[填写测试人员]
- 浏览器版本：[填写浏览器版本]

### 测试结果
- 基础功能：✅ 通过 / ❌ 失败
- 交互功能：✅ 通过 / ❌ 失败
- 视觉效果：✅ 通过 / ❌ 失败
- 兼容性：✅ 通过 / ❌ 失败

### 问题记录
如发现问题，请详细记录：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果
5. 截图或录屏

### 总体评价
- 功能完整性：⭐⭐⭐⭐⭐
- 用户体验：⭐⭐⭐⭐⭐
- 视觉效果：⭐⭐⭐⭐⭐
- 性能表现：⭐⭐⭐⭐⭐

## 验证完成标准

当以下所有项目都通过验证时，可认为功能验证完成：

- ✅ 所有分类标签正确显示
- ✅ 颜色映射完全正确
- ✅ 表格布局美观合理
- ✅ 原有功能完全正常
- ✅ 多浏览器兼容性良好
- ✅ 性能表现符合预期
- ✅ 用户体验良好

## 注意事项

1. **测试环境**：确保项目正常启动且无错误
2. **数据准备**：验证前确认测试数据完整
3. **浏览器缓存**：必要时清除浏览器缓存
4. **网络状态**：确保网络连接稳定
5. **版本一致**：确保使用最新代码版本
