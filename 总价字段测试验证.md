# 总价字段测试验证

## 测试环境
- 项目地址：http://localhost:3002/
- 测试页面：采购需求 -> 标项1 -> 标的1编辑
- 浏览器：Chrome/Firefox/Safari 最新版本

## 快速验证步骤

### 1. 打开标的1编辑弹窗
1. 访问 http://localhost:3002/
2. 点击"采购需求"标签页
3. 在标项1的标的1表格中点击"编辑"按钮
4. 验证编辑弹窗是否正常打开

### 2. 验证总价字段显示
检查编辑表单是否包含以下字段（从上到下）：
- ✅ 标的名称
- ✅ 采购目录
- ✅ 最高限制单价（元）
- ✅ 数量
- ✅ 单位
- ✅ 所属行业
- ✅ **总价（元）**（新增字段）

### 3. 验证总价字段功能
#### 3.1 字段基本功能
- ✅ 总价字段正确显示
- ✅ 输入框类型为数字输入框
- ✅ 字段标记为必填（有*标识）
- ✅ 输入框宽度占满容器

#### 3.2 建议总价计算
- ✅ 输入框下方显示计算提示
- ✅ 提示格式：`建议总价 = 最高限制单价 × 数量 = X × Y = Z 元`
- ✅ 计算结果准确（保留2位小数）

#### 3.3 实时计算验证
1. 修改"最高限制单价"字段
2. 观察建议总价是否实时更新
3. 修改"数量"字段
4. 观察建议总价是否实时更新

### 4. 验证数据初始化
1. 关闭编辑弹窗
2. 重新打开编辑弹窗
3. 检查总价字段是否有初始值
4. 验证初始值是否等于单价×数量

### 5. 验证表单验证
#### 5.1 必填验证
1. 清空总价字段
2. 点击"保存"按钮
3. 验证是否显示"请输入总价"错误提示

#### 5.2 数值验证
1. 输入0或负数
2. 点击"保存"按钮
3. 验证是否显示"总价必须大于0"错误提示

#### 5.3 小数精度验证
1. 输入带有多位小数的数值（如123.456789）
2. 验证是否自动保留2位小数（123.46）

## 详细测试用例

### 测试用例1：字段显示和布局
**目标**：验证总价字段正确显示在表单中

**步骤**：
1. 打开标的1编辑弹窗
2. 检查表单字段顺序
3. 验证总价字段的位置和样式

**预期结果**：
- 总价字段位于所属行业字段之后
- 字段标签为"总价（元）"
- 有必填标识（*）
- 输入框为数字类型

### 测试用例2：建议总价计算
**目标**：验证建议总价计算的准确性

**测试数据**：
| 单价 | 数量 | 预期建议总价 |
|------|------|-------------|
| 5000 | 10 | 50000.00 |
| 1200.5 | 3 | 3601.50 |
| 999.99 | 1 | 999.99 |

**步骤**：
1. 设置单价和数量
2. 检查建议总价计算结果
3. 验证计算公式显示

**预期结果**：
- 计算结果准确
- 保留2位小数
- 公式显示清晰

### 测试用例3：实时更新功能
**目标**：验证单价或数量变化时建议总价实时更新

**步骤**：
1. 设置初始单价为1000，数量为5
2. 修改单价为2000
3. 观察建议总价变化
4. 修改数量为10
5. 观察建议总价变化

**预期结果**：
- 单价变化：1000×5=5000 → 2000×5=10000
- 数量变化：2000×5=10000 → 2000×10=20000
- 更新及时，无延迟

### 测试用例4：数据保存和读取
**目标**：验证总价数据的保存和读取功能

**步骤**：
1. 输入总价为60000
2. 点击"保存"按钮
3. 重新打开编辑弹窗
4. 检查总价字段值

**预期结果**：
- 保存操作成功
- 重新打开时总价值正确显示
- 数据持久化正常

### 测试用例5：表单验证
**目标**：验证总价字段的各种验证规则

**子测试5.1：必填验证**
- 清空总价字段 → 保存 → 显示必填错误

**子测试5.2：最小值验证**
- 输入0 → 保存 → 显示最小值错误
- 输入-100 → 保存 → 显示最小值错误

**子测试5.3：有效值验证**
- 输入0.01 → 保存 → 验证通过
- 输入999999.99 → 保存 → 验证通过

## 边界测试

### 极值测试
| 测试值 | 预期行为 |
|--------|----------|
| 0 | 验证失败，显示错误提示 |
| 0.01 | 验证通过，最小有效值 |
| 999999.99 | 验证通过，大数值处理 |
| -1 | 验证失败，负数不允许 |

### 精度测试
| 输入值 | 预期显示 |
|--------|----------|
| 123.456 | 123.46 |
| 100.001 | 100.00 |
| 99.999 | 100.00 |

### 特殊输入测试
| 输入内容 | 预期行为 |
|----------|----------|
| 空字符串 | 必填验证错误 |
| 字母abc | 输入框拒绝输入 |
| 特殊字符!@# | 输入框拒绝输入 |

## 样式验证

### 布局检查
- ✅ 总价字段与其他字段对齐
- ✅ 标签宽度一致（140px）
- ✅ 输入框宽度占满容器
- ✅ 字段间距合理（20px）

### 提示框样式
- ✅ 提示框背景色为浅灰色
- ✅ 边框样式正确
- ✅ 文字大小和颜色合适
- ✅ 与输入框间距适当

### 响应式测试
- ✅ 不同窗口大小下布局正常
- ✅ 移动端显示效果良好
- ✅ 字体大小适配屏幕

## 兼容性测试

### 浏览器兼容性
- ✅ Chrome 最新版本
- ✅ Firefox 最新版本
- ✅ Safari 最新版本
- ✅ Edge 最新版本

### 功能兼容性
- ✅ 与现有字段无冲突
- ✅ 表单验证正常工作
- ✅ 数据保存读取正常

## 性能测试

### 计算性能
- ✅ 建议总价计算响应及时
- ✅ 实时更新无明显延迟
- ✅ 大数值计算准确

### 内存使用
- ✅ 无内存泄漏
- ✅ 组件销毁正常
- ✅ 事件监听清理完整

## 问题记录模板

如果在测试过程中发现问题，请按以下格式记录：

```
问题编号：TP001
问题标题：[简短描述]
严重程度：高/中/低
测试用例：[相关测试用例编号]
复现步骤：
1. 
2. 
3. 

预期结果：
实际结果：
浏览器信息：
截图/录屏：
```

## 验证清单

### 基础功能 ✅
- [ ] 总价字段正确显示
- [ ] 字段位置和样式正确
- [ ] 必填标识显示
- [ ] 输入框类型正确

### 计算功能 ✅
- [ ] 建议总价计算准确
- [ ] 实时更新正常
- [ ] 计算公式显示清晰
- [ ] 小数精度处理正确

### 验证功能 ✅
- [ ] 必填验证生效
- [ ] 最小值验证生效
- [ ] 数值格式验证生效
- [ ] 错误提示友好

### 数据功能 ✅
- [ ] 数据初始化正确
- [ ] 数据保存正常
- [ ] 数据读取正确
- [ ] 数据持久化有效

### 界面效果 ✅
- [ ] 布局美观协调
- [ ] 样式一致性好
- [ ] 响应式效果良好
- [ ] 用户体验友好

## 验证完成标准

当以下所有项目都通过验证时，可认为功能验证完成：

- ✅ 总价字段正确显示和工作
- ✅ 建议总价计算准确无误
- ✅ 实时更新功能正常
- ✅ 表单验证规则生效
- ✅ 数据保存读取正常
- ✅ 界面样式美观协调
- ✅ 多浏览器兼容性良好
- ✅ 性能表现符合预期

## 注意事项

1. **测试环境**：确保项目正常启动且无错误
2. **数据准备**：使用真实的测试数据进行验证
3. **浏览器缓存**：必要时清除浏览器缓存
4. **网络状态**：确保网络连接稳定
5. **版本一致**：确保使用最新代码版本
