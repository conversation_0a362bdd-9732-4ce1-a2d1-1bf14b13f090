# 标的3物业地址和管理内容清单测试验证

## 测试环境
- 项目地址：http://localhost:3002/
- 测试页面：采购需求 -> 标项1 -> 标的3编辑
- 浏览器：Chrome/Firefox/Safari 最新版本

## 快速验证步骤

### 1. 导航到标的3编辑
1. 访问 http://localhost:3002/
2. 点击"采购需求"标签页
3. 在标项1的标的3中点击"编辑"按钮
4. 验证编辑抽屉是否正常打开

### 2. 验证新增字段显示
检查编辑表单是否包含新增的字段：
- ✅ 物业地址字段（位于服务内容之后）
- ✅ 物业管理内容清单字段（设置操作）

### 3. 验证字段顺序
确认表单字段的正确顺序：
1. 标的名称
2. 采购目录
3. 最高限制单价（元）
4. 服务期限
5. 所属行业
6. 服务内容
7. **物业地址** 🆕
8. **物业管理内容清单** 🆕
9. 总价（元）

## 详细测试用例

### 测试用例1：物业地址字段验证
**目标**：验证物业地址字段的基本功能

#### 1.1 字段显示验证
**步骤**：
1. 打开标的3编辑弹窗
2. 检查物业地址字段是否正确显示

**预期结果**：
- 字段标签显示"物业地址 *"
- 输入框类型为文本输入
- 有必填标识（*）
- 支持清空功能

#### 1.2 数据初始化验证
**步骤**：
1. 打开编辑弹窗
2. 检查物业地址字段的初始值

**预期结果**：
- 显示默认地址："北京市朝阳区建国门外大街1号"

#### 1.3 验证规则测试
**步骤**：
1. 清空物业地址字段
2. 尝试保存表单
3. 输入少于5个字符的地址
4. 尝试保存表单

**预期结果**：
- 空值时显示"请输入物业地址"
- 少于5字符时显示"物业地址至少5个字符"

### 测试用例2：物业管理内容清单字段验证
**目标**：验证管理内容清单设置功能

#### 2.1 设置区域显示验证
**步骤**：
1. 打开标的3编辑弹窗
2. 检查物业管理内容清单字段

**预期结果**：
- 字段标签显示"物业管理内容清单 *"
- 显示设置状态信息
- 显示"设置"按钮
- 设置区域有浅灰色背景

#### 2.2 状态显示验证
**步骤**：
1. 检查初始状态显示

**预期结果**：
- 显示"已配置 3 项管理内容"（绿色文字）
- 或显示"暂未配置管理内容"（灰色文字）

### 测试用例3：物业管理内容清单弹窗验证
**目标**：验证管理内容清单弹窗的功能

#### 3.1 弹窗打开验证
**步骤**：
1. 点击"设置"按钮
2. 观察弹窗是否正常打开

**预期结果**：
- 弹窗正常打开
- 弹窗标题为"物业管理内容清单"
- 弹窗宽度为800px
- 显示头部操作区和表格区域

#### 3.2 默认数据验证
**步骤**：
1. 打开管理内容清单弹窗
2. 检查预置的管理内容

**预期结果**：
- 显示3条预置管理内容：
  1. 保洁服务（基础服务，每日）
  2. 安保服务（安全服务，24小时）
  3. 设施维修（维修服务，按需）

#### 3.3 表格结构验证
**步骤**：
1. 检查表格列结构

**预期结果**：
- 序号列（60px）
- 服务分类列（120px，下拉选择）
- 服务名称列（120px，文本输入）
- 服务描述列（200px+，文本输入）
- 服务频次列（100px，下拉选择）
- 服务标准列（150px+，文本输入）
- 操作列（80px，删除按钮）

### 测试用例4：新增管理内容功能
**目标**：验证新增管理内容的功能

**步骤**：
1. 点击"新增管理内容"按钮
2. 在新增行中填写信息：
   - 服务分类：选择"绿化服务"
   - 服务名称：输入"园林养护"
   - 服务描述：输入"植物修剪、浇水、施肥等园林维护工作"
   - 服务频次：选择"每周"
   - 服务标准：输入"符合园林绿化标准"
3. 点击"保存"按钮

**预期结果**：
- 新增行正确添加到表格
- 所有字段可正常编辑
- 保存成功并显示提示信息
- 弹窗关闭后状态更新为"已配置 4 项管理内容"

### 测试用例5：编辑管理内容功能
**目标**：验证编辑现有管理内容的功能

**步骤**：
1. 打开管理内容清单弹窗
2. 修改第一行的服务描述
3. 修改第二行的服务频次
4. 点击"保存"按钮

**预期结果**：
- 表格中的内容可直接编辑
- 下拉选择框正常工作
- 修改内容正确保存
- 显示保存成功提示

### 测试用例6：删除管理内容功能
**目标**：验证删除管理内容的功能

**步骤**：
1. 打开管理内容清单弹窗
2. 点击某一行的"删除"按钮
3. 点击"保存"按钮

**预期结果**：
- 对应行立即从表格中移除
- 序号自动重新排列
- 保存后状态更新为正确的数量

### 测试用例7：下拉选择选项验证
**目标**：验证下拉选择框的选项

#### 7.1 服务分类选项
**步骤**：
1. 点击服务分类下拉框
2. 查看可选选项

**预期选项**：
- 基础服务
- 安全服务
- 维修服务
- 绿化服务
- 其他服务

#### 7.2 服务频次选项
**步骤**：
1. 点击服务频次下拉框
2. 查看可选选项

**预期选项**：
- 每日
- 每周
- 每月
- 按需
- 24小时

### 测试用例8：弹窗关闭功能
**目标**：验证弹窗的关闭功能

**步骤**：
1. 点击"取消"按钮
2. 点击弹窗右上角关闭按钮
3. 点击弹窗外部区域

**预期结果**：
- 所有方式都能正常关闭弹窗
- 关闭时不保存未确认的修改
- 下次打开时恢复上次保存的状态

### 测试用例9：数据保存验证
**目标**：验证整体数据保存功能

**步骤**：
1. 修改物业地址为"上海市浦东新区陆家嘴环路1000号"
2. 配置管理内容清单（新增1项，修改1项）
3. 修改其他字段信息
4. 点击"保存"按钮
5. 重新打开编辑弹窗

**预期结果**：
- 物业地址正确保存和显示
- 管理内容清单状态正确更新
- 所有修改的信息都正确保存
- 重新打开时显示最新数据

## 界面样式验证

### 物业地址字段样式
- ✅ 与其他字段样式一致
- ✅ 输入框宽度占满容器
- ✅ 标签对齐正确
- ✅ 必填标识显示

### 管理内容设置区域样式
- ✅ 浅灰色背景卡片
- ✅ 左右布局合理
- ✅ 状态文字颜色正确
- ✅ 设置按钮样式正确

### 管理内容清单弹窗样式
- ✅ 弹窗宽度800px
- ✅ 头部布局美观
- ✅ 表格样式清晰
- ✅ 按钮对齐正确

## 兼容性测试

### 浏览器兼容性
- ✅ Chrome 最新版本
- ✅ Firefox 最新版本
- ✅ Safari 最新版本
- ✅ Edge 最新版本

### 功能兼容性
- ✅ 与其他标的编辑功能无冲突
- ✅ 与现有表单验证机制兼容
- ✅ 数据保存和读取正常

## 性能测试

### 响应性能
- ✅ 弹窗打开关闭流畅
- ✅ 表格编辑响应及时
- ✅ 数据保存处理快速
- ✅ 状态更新实时

### 数据处理
- ✅ 大量管理内容项处理正常
- ✅ 表格滚动性能良好
- ✅ 内存使用合理

## 验证清单

### 基础功能 ✅
- [ ] 物业地址字段正常显示和工作
- [ ] 管理内容清单设置功能正常
- [ ] 弹窗打开关闭正常
- [ ] 数据初始化正确

### 编辑功能 ✅
- [ ] 新增管理内容功能正常
- [ ] 编辑管理内容功能正常
- [ ] 删除管理内容功能正常
- [ ] 下拉选择功能正常

### 验证功能 ✅
- [ ] 物业地址验证规则生效
- [ ] 必填字段验证正常
- [ ] 错误提示友好
- [ ] 数据格式验证正确

### 界面效果 ✅
- [ ] 字段布局美观
- [ ] 样式一致性好
- [ ] 响应式效果良好
- [ ] 用户体验友好

## 问题记录

如发现问题，请按以下格式记录：

```
问题编号：T3PM001
问题标题：[简短描述]
严重程度：高/中/低
测试用例：[相关测试用例]
复现步骤：
1. 
2. 
3. 

预期结果：
实际结果：
浏览器信息：
截图/录屏：
```

## 验证完成标准

当以下所有项目都通过验证时，可认为功能验证完成：

- ✅ 物业地址字段功能完全正常
- ✅ 管理内容清单设置功能完全正常
- ✅ 弹窗编辑功能正常工作
- ✅ 数据验证规则有效
- ✅ 数据保存更新正确
- ✅ 界面样式美观协调
- ✅ 兼容性测试通过
- ✅ 性能表现符合预期

## 注意事项

1. **测试数据**：使用真实的物业地址和管理内容进行测试
2. **操作顺序**：按照实际使用场景进行测试
3. **边界情况**：注意测试各种边界和异常情况
4. **数据完整性**：确保所有数据正确保存和读取
5. **用户体验**：重点关注操作的便利性和直观性
