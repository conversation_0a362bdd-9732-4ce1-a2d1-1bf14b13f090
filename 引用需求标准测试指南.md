# 引用需求标准功能测试指南

## 测试环境
- 浏览器：Chrome/Firefox/Safari 最新版本
- 项目地址：http://localhost:3002/
- 测试页面：采购需求 -> 标项1 -> 技术要求 -> 引用需求标准

## 测试前准备

### 1. 启动项目
```bash
npm run dev
```
确保项目在 http://localhost:3002/ 正常运行

### 2. 导航到测试页面
1. 打开浏览器访问项目地址
2. 点击"采购需求"标签页
3. 在标项1中找到"技术要求"卡片
4. 点击"编辑配置"按钮
5. 在技术要求编辑抽屉中点击"引用需求标准"按钮

## 功能测试用例

### 测试用例1：弹窗基本功能
**测试目标**：验证弹窗的打开、关闭和基本布局

**测试步骤**：
1. 点击"引用需求标准"按钮
2. 验证弹窗是否正常打开
3. 检查弹窗标题是否为"引用需求标准"
4. 验证弹窗尺寸是否合适（1200px宽）
5. 点击右上角关闭按钮
6. 验证弹窗是否正常关闭

**预期结果**：
- ✅ 弹窗正常打开和关闭
- ✅ 布局完整，无样式错误
- ✅ 标题显示正确

### 测试用例2：搜索功能
**测试目标**：验证顶部搜索区域的各项功能

**测试步骤**：
1. 打开引用需求标准弹窗
2. 在"需求标准名称"输入框输入"操作系统"
3. 在"采购目录"下拉框选择"数据库政府采购需求标准"
4. 在"指标名称"输入框输入"CPU"
5. 点击"搜索"按钮
6. 验证搜索提示信息
7. 点击"重置"按钮
8. 验证所有搜索条件是否清空

**预期结果**：
- ✅ 所有输入框和下拉框正常工作
- ✅ 搜索按钮点击有反馈
- ✅ 重置功能正常清空表单

### 测试用例3：左侧采购目录
**测试目标**：验证左侧采购目录的展示和交互

**测试步骤**：
1. 检查左侧是否显示采购目录列表
2. 验证每个目录项是否包含：
   - 目录名称
   - 年版信息
   - 编码信息
   - 已选数量
3. 点击不同的采购目录项
4. 验证选中状态是否正确切换
5. 检查右侧内容是否随之更新

**预期结果**：
- ✅ 显示4个采购目录分类
- ✅ 每个目录信息完整
- ✅ 点击切换正常，有视觉反馈
- ✅ 右侧内容正确更新

### 测试用例4：右侧数据表格
**测试目标**：验证右侧标准数据表格的显示和功能

**测试步骤**：
1. 检查表格是否正确显示以下列：
   - 复选框列
   - 指标名称列
   - 指标要求列
   - 使用说明列
   - 其他属性列
2. 验证数据是否正确显示
3. 检查表格头部信息：
   - 当前分类名称
   - 总条数统计
4. 测试表格滚动功能

**预期结果**：
- ✅ 表格结构完整，列显示正确
- ✅ 数据内容准确
- ✅ 头部信息正确
- ✅ 滚动功能正常

### 测试用例5：单选功能
**测试目标**：验证单个标准的选择功能

**测试步骤**：
1. 在表格中勾选第一行的复选框
2. 检查底部是否显示"已选择 1 条需求标准"
3. 再勾选第二行的复选框
4. 检查底部是否显示"已选择 2 条需求标准"
5. 取消第一行的勾选
6. 检查底部是否显示"已选择 1 条需求标准"

**预期结果**：
- ✅ 复选框正常工作
- ✅ 底部统计实时更新
- ✅ 选择状态正确维护

### 测试用例6：批量操作
**测试目标**：验证全选和清空功能

**测试步骤**：
1. 点击"全选当页"按钮
2. 验证当前页面所有行是否被选中
3. 检查底部统计是否正确
4. 点击"清空选择"按钮
5. 验证所有选择是否被清空
6. 检查底部统计是否归零

**预期结果**：
- ✅ 全选功能正常工作
- ✅ 清空功能正常工作
- ✅ 统计信息准确

### 测试用例7：跨分类选择
**测试目标**：验证在不同分类间切换时选择状态的保持

**测试步骤**：
1. 在"数据库政府采购需求标准"分类中选择2条标准
2. 切换到"操作系统政府采购需求标准"分类
3. 选择1条标准
4. 检查底部是否显示"已选择 3 条需求标准"
5. 切换回"数据库政府采购需求标准"分类
6. 验证之前的选择是否保持
7. 检查左侧目录项的"已选"数量是否正确

**预期结果**：
- ✅ 跨分类选择状态正确保持
- ✅ 总计数量准确
- ✅ 分类选择数量正确显示

### 测试用例8：引用功能
**测试目标**：验证将选择的标准引用到技术要求列表

**测试步骤**：
1. 选择3-5条不同分类的标准
2. 点击"引用"按钮
3. 验证是否显示成功提示信息
4. 检查弹窗是否自动关闭
5. 在技术要求列表中查看是否新增了对应的技术要求项
6. 验证新增项的内容是否正确：
   - 分类为"引用标准"
   - 指标模式为"二级指标"
   - 一级指标、二级指标、要求内容正确

**预期结果**：
- ✅ 引用操作成功
- ✅ 弹窗正确关闭
- ✅ 技术要求列表正确更新
- ✅ 引用内容准确

### 测试用例9：错误处理
**测试目标**：验证异常情况的处理

**测试步骤**：
1. 不选择任何标准，直接点击"引用"按钮
2. 验证是否显示警告提示："请先选择需要引用的标准"
3. 验证弹窗是否保持打开状态

**预期结果**：
- ✅ 显示正确的警告信息
- ✅ 弹窗保持打开，允许用户继续操作

### 测试用例10：界面响应性
**测试目标**：验证界面在不同屏幕尺寸下的表现

**测试步骤**：
1. 在不同浏览器窗口大小下测试弹窗
2. 验证布局是否保持合理
3. 检查表格是否有水平滚动条（当内容过宽时）
4. 验证左侧目录区域是否保持固定宽度

**预期结果**：
- ✅ 布局在不同尺寸下保持合理
- ✅ 表格正确处理内容溢出
- ✅ 左侧目录宽度固定

## 性能测试

### 测试用例11：大数据量处理
**测试目标**：验证在数据量较大时的性能表现

**测试步骤**：
1. 切换到数据量最多的分类
2. 测试表格滚动的流畅性
3. 测试批量选择的响应速度
4. 观察内存使用情况

**预期结果**：
- ✅ 滚动流畅，无明显卡顿
- ✅ 批量操作响应及时
- ✅ 内存使用合理

## 兼容性测试

### 浏览器兼容性
- Chrome 最新版本
- Firefox 最新版本
- Safari 最新版本
- Edge 最新版本

### 测试要点
- 弹窗显示正常
- 交互功能完整
- 样式一致性
- 性能表现

## 问题记录模板

如果在测试过程中发现问题，请按以下格式记录：

```
问题编号：P001
问题标题：[简短描述]
严重程度：高/中/低
复现步骤：
1. 
2. 
3. 

预期结果：
实际结果：
浏览器信息：
截图/录屏：
```

## 测试完成标准

- ✅ 所有测试用例通过
- ✅ 无严重功能缺陷
- ✅ 界面美观，用户体验良好
- ✅ 性能表现符合预期
- ✅ 兼容性测试通过

## 注意事项

1. 测试前确保项目正常启动
2. 建议使用最新版本的现代浏览器
3. 注意观察控制台是否有错误信息
4. 测试时注意网络连接状态
5. 如遇到问题，请详细记录复现步骤
