# 标的2编辑功能说明

## 功能概述

参照标的1的基础信息编辑弹窗，为标的2（项目采购-工程类）配置了相应的编辑弹窗功能，支持工程类项目的基础信息编辑和管理。

## 功能特点

### 1. 工程类项目适配
- **项目类型**：专门针对工程类项目设计
- **字段优化**：根据工程项目特点调整字段类型和验证规则
- **行业选择**：提供工程相关的行业选项

### 2. 编辑弹窗设计
- **弹窗尺寸**：500px宽度，适合工程项目信息展示
- **标题明确**：显示"编辑标的2基础信息"
- **布局合理**：标签宽度140px，左对齐布局

### 3. 表单字段配置
根据工程类项目特点，配置了以下字段：

| 字段名称 | 字段类型 | 验证规则 | 说明 |
|---------|---------|---------|------|
| 标的名称 | 文本输入 | 必填，最少2字符 | 工程项目名称 |
| 采购目录 | 文本输入 | 必填 | 工程采购目录编码 |
| 最高限制单价（元） | 数字输入 | 必填，>0，2位小数 | 工程单价限制 |
| 数量 | 文本输入 | 必填 | 支持"不限"、"1项"等 |
| 所属行业 | 下拉选择 | 必填 | 工程相关行业 |
| 总价（元） | 数字输入 | 必填，>0，2位小数 | 工程总价 |

## 界面设计

### 表单布局
```
┌─────────────────────────────────────┐
│ 编辑标的2基础信息                    │
├─────────────────────────────────────┤
│ 标的名称 *     [输入框]              │
│ 采购目录 *     [输入框]              │
│ 最高限制单价（元）* [数字输入框]      │
│ 数量 *         [输入框]              │
│ 所属行业 *     [下拉选择框]          │
│ 总价（元）*    [数字输入框]          │
│                [工程类项目提示]       │
├─────────────────────────────────────┤
│                    [取消] [保存]     │
└─────────────────────────────────────┘
```

### 工程类特色设计
- **数量字段**：使用文本输入，支持"不限"、"1项"等工程常用表述
- **行业选择**：提供工程、建筑、装修、市政、园林、水利、交通等选项
- **总价提示**：显示"工程类项目总价通常根据工程量清单和市场价格确定"

## 技术实现

### 1. 数据结构
```javascript
// 标的2编辑表单数据
const target2EditForm = ref({
  name: '',           // 标的名称
  catalog: '',        // 采购目录
  maxPrice: 0,        // 最高限制单价
  quantity: '',       // 数量（文本类型）
  industry: '',       // 所属行业
  totalPrice: 0       // 总价
})
```

### 2. 验证规则
```javascript
const target2EditFormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' },
    { min: 2, message: '标的名称至少2个字符', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请输入采购目录', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '最高限制单价必须大于0', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}
```

### 3. 核心方法
```javascript
// 打开编辑抽屉
const openTarget2EditDrawer = () => {
  const currentData = target2BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))
  
  target2EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    quantity: currentData.quantity,
    industry: currentData.industry,
    totalPrice: maxPrice // 工程类项目总价通常等于最高限制单价
  }
  target2EditDrawerVisible.value = true
}

// 保存表单数据
const saveTarget2EditForm = () => {
  target2BasicInfo.value[0] = {
    name: target2EditForm.value.name,
    catalog: target2EditForm.value.catalog,
    maxPrice: `${target2EditForm.value.maxPrice}元`,
    quantity: target2EditForm.value.quantity,
    industry: target2EditForm.value.industry,
    function: 'xxxxx',
    standard: 'xxxx'
  }
  
  ElMessage.success('标的2基础信息保存成功')
  handleTarget2DrawerClose()
}
```

## 工程类项目特点

### 1. 数量表述灵活
- **不限**：适用于按需采购的工程项目
- **1项**：适用于整体工程项目
- **具体数量**：适用于有明确数量要求的工程

### 2. 行业分类专业
提供工程相关的专业行业选项：
- **工程**：通用工程项目
- **建筑**：建筑工程
- **装修**：装修装饰工程
- **市政**：市政基础设施
- **园林**：园林绿化工程
- **水利**：水利水电工程
- **交通**：交通运输工程
- **其他**：其他类型工程

### 3. 价格计算特点
- **总价导向**：工程项目通常以总价为主要考量
- **单价参考**：最高限制单价作为控制标准
- **灵活调整**：支持根据工程量清单调整总价

## 与标的1的差异

### 相同点
- 基本的表单结构和验证逻辑
- 编辑抽屉的交互方式
- 数据保存和更新机制

### 差异点
| 特性 | 标的1（设备采购） | 标的2（工程类） |
|------|------------------|----------------|
| 数量字段 | 数字输入+单位选择 | 文本输入（支持"不限"） |
| 行业选择 | 设备相关行业 | 工程相关行业 |
| 总价计算 | 单价×数量 | 根据工程量清单 |
| 抽屉宽度 | 600px | 500px |
| 总价提示 | 自动计算建议 | 工程类项目说明 |

## 使用流程

### 1. 打开编辑功能
1. 在标的2表格中点击"编辑"按钮
2. 系统打开编辑抽屉
3. 自动填充当前数据到表单

### 2. 编辑基础信息
1. **标的名称**：修改工程项目名称
2. **采购目录**：更新采购目录编码
3. **最高限制单价**：设置单价上限
4. **数量**：输入项目数量（支持文本描述）
5. **所属行业**：选择工程相关行业
6. **总价**：输入项目总价

### 3. 保存更新
1. 系统验证表单数据
2. 验证通过后更新数据
3. 显示保存成功提示
4. 自动关闭编辑抽屉

## 数据验证

### 必填字段验证
- 所有字段均为必填
- 空值时显示相应错误提示

### 数值字段验证
- 最高限制单价必须大于0
- 总价必须大于0
- 支持小数点后2位精度

### 文本字段验证
- 标的名称最少2个字符
- 采购目录和数量不能为空

## 错误处理

### 验证错误
- 实时显示字段验证错误
- 错误提示信息友好明确
- 阻止无效数据保存

### 操作错误
- 网络异常时的错误提示
- 数据保存失败的处理
- 用户操作引导

## 扩展功能

### 已实现
- ✅ 完整的表单编辑功能
- ✅ 数据验证和错误提示
- ✅ 工程类项目特色适配
- ✅ 与现有系统的集成

### 待扩展
- 🔄 工程量清单导入功能
- 🔄 价格历史记录查看
- 🔄 工程进度跟踪集成
- 🔄 多标的批量编辑

## 测试要点

### 功能测试
1. 编辑按钮点击响应
2. 表单数据正确填充
3. 各字段验证规则生效
4. 数据保存和更新正常

### 界面测试
1. 抽屉正常打开关闭
2. 表单布局美观合理
3. 字段对齐和间距正确
4. 响应式效果良好

### 数据测试
1. 数据初始化正确
2. 编辑后数据更新
3. 验证规则有效性
4. 错误处理完整性

## 总结

标的2编辑功能的实现特点：

- 🎯 **专业适配**：针对工程类项目的特点进行专门设计
- 🔧 **技术复用**：基于标的1的成熟技术架构
- 🎨 **界面优化**：简洁明了的工程项目编辑界面
- 📊 **数据完整**：支持工程项目的完整信息管理
- 🚀 **易于使用**：直观的操作流程和友好的用户体验

这个功能为工程类项目的采购管理提供了专业的编辑工具，提高了工程采购的信息管理效率和准确性。
