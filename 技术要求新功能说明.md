# 技术要求详细信息配置列表新功能说明

## 功能概述

在技术要求的详细信息配置列表中，新增了两个重要功能：
1. **对表国标要求** - 用于对比当前配置与国家标准的差异
2. **属性值参考** - 提供常用属性值的快速选择和参考

## 功能详细说明

### 1. 对表国标要求功能

#### 功能位置
- 在技术要求编辑抽屉的表格操作列中
- 点击"对表国标要求"按钮打开弹窗

#### 功能特点
- **当前配置展示**：显示当前技术要求的分类、指标名称和具体要求
- **国标对比表格**：展示相关国家标准的详细信息
  - 国标编号（如：GB/T 28181-2016）
  - 国标名称
  - 国标具体要求
  - 差异分析（无差异/轻微差异/重大差异）
  - 建议措施
- **差异可视化**：通过不同颜色的标签直观显示差异程度
  - 绿色：无差异
  - 橙色：轻微差异  
  - 红色：重大差异
- **应用功能**：可以将国标要求应用到当前配置中

#### 使用场景
- 确保采购要求符合国家标准
- 识别配置与国标的差异点
- 获得合规性建议

### 2. 属性值参考功能

#### 功能位置
- 在技术要求编辑抽屉的表格操作列中
- 点击"属性值参考"按钮打开弹窗

#### 功能特点
- **当前指标信息**：显示正在编辑的指标名称和当前要求
- **多种参考来源**：
  - **常用参数值**：按分类展示常用的技术参数
    - CPU性能等级
    - 内存规格
    - 网络性能等
  - **行业标准值**：不同行业的标准配置要求
    - 政府机关
    - 金融行业
    - 教育行业
    - 医疗行业
  - **历史配置**：以往项目的配置记录
    - 项目名称
    - 配置值
    - 配置时间
- **快速选择**：点击标签或选择按钮快速应用属性值
- **智能应用**：选择的属性值会自动填入当前技术要求中

#### 使用场景
- 快速填写常用的技术参数
- 参考行业标准配置
- 复用历史项目的成功配置
- 提高配置效率和准确性

## 技术实现

### 前端组件
- 使用Element Plus的Dialog组件实现弹窗
- 采用响应式设计，支持不同屏幕尺寸
- 使用Vue 3的Composition API进行状态管理

### 数据结构
- 国标对比数据包含标准编号、名称、要求、差异分析等字段
- 属性值参考数据按类型分类，支持多种来源
- 支持动态数据加载和更新

### 交互设计
- 直观的操作按钮布局
- 清晰的信息展示结构
- 友好的用户反馈机制

## 使用流程

### 对表国标要求
1. 在技术要求列表中找到需要对比的项目
2. 点击"对表国标要求"按钮
3. 查看当前配置与国标的对比结果
4. 根据差异分析和建议进行调整
5. 可选择应用国标要求或关闭弹窗

### 属性值参考
1. 在技术要求列表中找到需要填写的项目
2. 点击"属性值参考"按钮
3. 在不同标签页中浏览参考值：
   - 常用参数值：点击标签快速选择
   - 行业标准值：点击"选择"按钮应用
   - 历史配置：点击"选择"按钮复用
4. 点击"应用选择的值"确认应用
5. 返回技术要求列表查看更新结果

## 优势特点

1. **提高效率**：减少手动输入，快速选择常用配置
2. **确保合规**：对比国家标准，避免配置偏差
3. **经验复用**：利用历史配置和行业标准
4. **直观对比**：可视化差异分析，便于决策
5. **灵活应用**：支持选择性应用和自定义调整

## 后续扩展

- 支持更多国家标准和行业标准
- 增加智能推荐功能
- 支持批量对比和应用
- 集成更多历史数据源
- 添加配置模板功能
