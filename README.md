# 通用管理系统

一个基于 Vue 3 + TypeScript + Element Plus 的通用管理系统框架。

## 项目介绍

本项目是一个现代化的后台管理系统模板，采用最新的前端技术栈构建，提供了完整的管理系统基础功能。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供静态类型检查
- **Element Plus** - 基于 Vue 3 的组件库
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js 官方路由管理器
- **Pinia** - Vue 的状态管理库

## 功能特性

- 🎨 现代化 UI 设计，响应式布局
- 🔐 完整的权限管理系统
- 📱 移动端适配
- 🌍 国际化支持准备
- 📊 数据可视化组件
- 🛠️ 丰富的业务组件
- 📝 详细的文档说明

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── views/          # 页面视图
├── store/          # 状态管理
├── types/          # TypeScript 类型定义
├── styles/         # 全局样式
├── App.vue
└── main.ts
```

## 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
