# 标的3对比国标要求和属性值参考功能说明

## 功能概述

参照标的1的技术要求中的"对比国标要求"和"属性值参考"功能，为标的3的服务要求添加了这两个重要操作，支持服务要求与国家标准的对比分析和常用属性值的快速引用。

## 功能实现

### 1. 界面功能添加

#### 操作按钮
在标的3的服务要求编辑表格中添加了两个操作按钮：

```vue
<el-table-column label="操作" width="200" align="center">
  <template #default="scope">
    <div class="service-actions">
      <el-button
        v-if="currentEditingTarget === 'target3'"
        type="primary"
        link
        size="small"
        @click="openServiceStandardComparisonDialog(scope.row, scope.$index)"
      >
        对比国标要求
      </el-button>
      <el-button
        v-if="currentEditingTarget === 'target3'"
        type="info"
        link
        size="small"
        @click="openServiceAttributeReferenceDialog(scope.row, scope.$index)"
      >
        属性值参考
      </el-button>
      <el-button
        type="danger"
        link
        size="small"
        @click="removeServiceRequirementItem(scope.$index)"
      >
        删除
      </el-button>
    </div>
  </template>
</el-table-column>
```

#### 条件渲染
- 按钮仅在编辑标的3时显示（`v-if="currentEditingTarget === 'target3'"`）
- 保持与标的1技术要求一致的界面风格和操作体验

### 2. 服务类国标对比数据

#### 国标对比数据结构
```javascript
const serviceStandardComparisonList = ref([
  {
    id: 1,
    standardCode: 'GB/T 20647.9-2006',
    standardName: '社区服务指南 第9部分：物业服务',
    standardRequirement: '保洁服务应每日清洁不少于1次，垃圾清理应及时',
    differenceLevel: 'none',
    differenceDescription: '当前配置符合国标要求，清洁频次为每日2次',
    suggestion: '配置符合并超出国标要求，建议保持'
  },
  // ... 更多国标对比数据
])
```

#### 配置的国标对比内容

1. **物业服务标准（GB/T 20647.9-2006）**
   - 国标要求：保洁服务应每日清洁不少于1次，垃圾清理应及时
   - 差异分析：当前配置符合国标要求，清洁频次为每日2次
   - 建议：配置符合并超出国标要求，建议保持

2. **建设工程项目管理规范（GB/T 50326-2017）**
   - 国标要求：服务人员应具备相应的专业资质和技能
   - 差异分析：要求持有物业管理师证书，符合国标要求
   - 建议：配置合规，无需调整

3. **建设工程文件归档规范（GB/T 50328-2014）**
   - 国标要求：应急响应时间一般问题不超过4小时
   - 差异分析：当前要求2小时响应，标准更高
   - 建议：当前配置优于国标要求，建议保持

4. **质量管理体系要求（GB/T 19001-2016）**
   - 国标要求：应建立质量管理体系，确保服务质量
   - 差异分析：要求ISO9001认证，完全符合国标
   - 建议：配置合规，无需调整

### 3. 服务类属性值参考数据

#### 常用属性值数据
```javascript
{
  name: '服务时间标准',
  values: [
    { id: 15, text: '7×24小时值守服务' },
    { id: 16, text: '工作日8:00-18:00服务' },
    { id: 17, text: '工作日8:00-20:00服务' },
    { id: 18, text: '全年无休服务' }
  ]
}
```

#### 配置的属性值分类

1. **服务时间标准**
   - 7×24小时值守服务
   - 工作日8:00-18:00服务
   - 工作日8:00-20:00服务
   - 全年无休服务

2. **响应时间要求**
   - 一般问题2小时内响应
   - 一般问题4小时内响应
   - 紧急问题30分钟内响应
   - 紧急问题1小时内响应

3. **清洁频次标准**
   - 每日清洁1次
   - 每日清洁2次
   - 每日清洁3次
   - 按需清洁

4. **人员资质要求**
   - 持有物业管理师证书
   - 持有相关专业资格证书
   - 具备3年以上工作经验
   - 具备5年以上工作经验

#### 行业标准值数据
```javascript
{
  industry: '物业服务',
  standardValue: '7×24小时值守，一般问题2小时内响应',
  description: '满足物业管理服务要求'
}
```

#### 历史配置值数据
```javascript
{
  projectName: '某大厦物业管理服务',
  configValue: '7×24小时值守服务，一般问题2小时内响应，紧急问题30分钟内到达',
  configTime: '2024-01-15'
}
```

## 技术实现

### 1. 方法实现

#### 对比国标要求方法
```javascript
// 服务要求对比国标要求
const openServiceStandardComparisonDialog = (serviceItem: any, index: number) => {
  currentTechnicalItem.value = { ...serviceItem }
  currentTechnicalIndex.value = index
  standardComparisonDialogVisible.value = true
}
```

#### 属性值参考方法
```javascript
// 服务要求属性值参考
const openServiceAttributeReferenceDialog = (serviceItem: any, index: number) => {
  currentTechnicalItem.value = { ...serviceItem }
  currentTechnicalIndex.value = index
  selectedAttributeValue.value = ''
  attributeReferenceDialogVisible.value = true
}
```

### 2. 动态数据选择

#### 国标对比数据选择
```javascript
// 当前显示的国标对比列表
const currentStandardComparisonList = computed(() => {
  if (currentEditingTarget.value === 'target3') {
    return serviceStandardComparisonList.value
  }
  return standardComparisonList.value
})
```

#### 属性值应用逻辑
```javascript
const applySelectedAttributeValue = () => {
  if (currentEditingTarget.value === 'target3') {
    // 将选择的属性值应用到当前服务要求中
    serviceList.value[currentTechnicalIndex.value].requirement = selectedAttributeValue.value
    ElMessage.success('属性值应用成功')
  } else {
    // 将选择的属性值应用到当前技术要求中
    technicalList.value[currentTechnicalIndex.value].requirement = selectedAttributeValue.value
    ElMessage.success('属性值应用成功')
  }
}
```

### 3. 界面适配

#### 指标名称显示适配
```vue
<span class="label">{{ currentEditingTarget === 'target3' ? '服务要求名称：' : '指标名称：' }}</span>
<span class="value">
  {{ currentEditingTarget === 'target3' 
      ? currentTechnicalItem?.serviceName
      : (currentTechnicalItem?.indicatorMode === 'single'
          ? currentTechnicalItem?.indicatorName
          : `${currentTechnicalItem?.primaryIndicator}/${currentTechnicalItem?.secondaryIndicator}`) }}
</span>
```

## 操作流程

### 1. 对比国标要求流程
```
选择服务要求 → 点击"对比国标要求" → 查看国标对比分析 → 了解差异和建议 → 关闭弹窗
```

### 2. 属性值参考流程
```
选择服务要求 → 点击"属性值参考" → 浏览常用属性值 → 选择合适的值 → 应用到服务要求 → 保存配置
```

## 功能特点

### 1. 完全参照标的1实现
- 🎯 **界面一致性**：按钮样式、位置、颜色与标的1完全一致
- 🎯 **操作流程**：操作流程与标的1技术要求完全相同
- 🎯 **功能逻辑**：智能识别编辑标的，自动使用对应数据

### 2. 服务类专业适配
- 🎯 **专业国标**：提供4个专业的服务类国家标准对比
- 🎯 **实用属性值**：提供4类常用的服务属性值参考
- 🎯 **行业标准**：包含物业、保洁、安保、餐饮等行业标准

### 3. 智能化处理
- 🎯 **自动识别**：根据currentEditingTarget自动选择服务类数据
- 🎯 **动态显示**：弹窗内容根据编辑标的类型动态调整
- 🎯 **智能应用**：属性值自动应用到正确的数据列表

### 4. 数据完整性
- 🎯 **国标覆盖**：涵盖物业服务的主要国家标准
- 🎯 **属性丰富**：提供服务时间、响应时间、清洁频次、人员资质等属性
- 🎯 **历史参考**：包含真实项目的历史配置数据

## 使用场景

### 1. 服务标准验证
- 验证服务要求是否符合国家标准
- 了解与国标的差异和改进建议
- 确保采购文件的合规性

### 2. 快速配置服务要求
- 快速选择常用的服务时间安排
- 引用标准的响应时间要求
- 使用规范的人员资质描述

### 3. 参考历史配置
- 查看类似项目的配置经验
- 参考行业标准配置
- 提高配置的专业性

## 优势分析

### 1. 提高合规性
- 确保服务要求符合国家标准
- 避免配置不合规的要求
- 提高采购文件质量

### 2. 提升效率
- 快速引用常用属性值
- 减少手动输入工作
- 标准化配置流程

### 3. 专业指导
- 提供专业的国标对比分析
- 给出具体的改进建议
- 帮助用户做出正确决策

### 4. 经验积累
- 积累历史配置经验
- 建立标准配置库
- 促进知识共享

## 扩展建议

### 1. 国标库扩展
- 增加更多服务类国家标准
- 细化不同服务类型的标准
- 定期更新标准内容

### 2. 属性值丰富
- 增加更多服务属性分类
- 提供更详细的属性值选项
- 支持自定义属性值

### 3. 智能推荐
- 根据服务类型推荐相关国标
- 智能匹配合适的属性值
- 基于历史数据推荐配置

## 总结

标的3的"对比国标要求"和"属性值参考"功能已完全实现，为服务类采购项目提供了专业的标准对比和属性值参考能力。功能完整、操作便捷，与标的1的技术要求功能保持高度一致，大大提高了服务要求配置的专业性和效率。
