# 标的2技术要求操作简化说明

## 功能概述

在标的2（项目采购-工程类）的技术要求配置中，删除了"引用需求标准"、"对比国标要求"和"属性值参考"操作，简化了工程类项目的技术要求配置流程，使其更符合工程项目的实际需求。

## 修改内容

### 1. 删除的操作按钮
在标的2的技术要求编辑界面中，以下操作按钮被移除：

#### 头部操作区域
- ❌ **引用需求标准**：删除了从标准库引用需求标准的功能

#### 表格操作列
- ❌ **对比国标要求**：删除了与国家标准对比的功能
- ❌ **属性值参考**：删除了属性值参考查询的功能

### 2. 保留的操作功能
标的2的技术要求配置仍保留以下核心功能：

#### 头部操作区域
- ✅ **新增技术要求**：手动添加技术要求
- ✅ **批量导入**：从Excel文件导入技术要求
- ✅ **导出配置**：导出当前技术要求配置

#### 表格操作列
- ✅ **编辑**：编辑单个技术要求
- ✅ **删除**：删除单个技术要求

## 技术实现

### 1. 条件渲染机制
通过`currentEditingTarget`变量来判断当前编辑的标的，实现条件渲染：

```vue
<!-- 引用需求标准按钮 - 仅标的1显示 -->
<el-button 
  v-if="currentEditingTarget === 'target1'"
  type="warning" 
  plain 
  size="small" 
  @click="referenceStandards"
>
  <el-icon><Document /></el-icon>
  引用需求标准
</el-button>

<!-- 对比国标要求按钮 - 仅标的1显示 -->
<el-button
  v-if="currentEditingTarget === 'target1'"
  type="primary"
  link
  size="small"
  @click="openStandardComparisonDialog(scope.row, scope.$index)"
>
  对比国标要求
</el-button>

<!-- 属性值参考按钮 - 仅标的1显示 -->
<el-button
  v-if="currentEditingTarget === 'target1'"
  type="info"
  link
  size="small"
  @click="openAttributeReferenceDialog(scope.row, scope.$index)"
>
  属性值参考
</el-button>
```

### 2. 标的识别机制
当打开标的2的技术要求编辑时，系统会设置：
```javascript
const editTarget2Technical = () => {
  technicalEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的2
  currentEditingTarget.value = 'target2'
}
```

### 3. 功能保留
- 所有相关的弹窗组件和方法仍然保留
- 标的1的功能完全不受影响
- 代码结构保持清晰和可维护

## 设计理念

### 1. 工程类项目特点
工程类项目与设备采购项目在技术要求方面有显著差异：

#### 设备采购（标的1）
- **标准化程度高**：设备规格相对标准化
- **国标依赖强**：大量依赖国家标准和行业标准
- **属性值固定**：设备参数相对固定和标准化
- **标准库丰富**：有完善的设备标准库可供引用

#### 工程类项目（标的2）
- **定制化程度高**：每个工程项目都有独特性
- **标准适用性低**：工程标准更多体现在施工规范而非需求标准
- **要求灵活多样**：工程要求更加灵活和多样化
- **现场条件影响**：受具体工程现场条件影响较大

### 2. 简化的必要性

#### 避免功能冗余
- 工程项目很少需要引用标准化的需求标准
- 国标对比在工程项目中应用场景有限
- 属性值参考对工程要求的指导意义不大

#### 提升用户体验
- 减少不必要的操作选项
- 简化界面，突出核心功能
- 降低用户学习成本

#### 符合实际需求
- 工程项目更注重手动定制化配置
- 批量导入更适合工程项目的需求管理
- 编辑和删除是工程要求管理的核心操作

## 功能对比

### 标的1（设备采购）技术要求操作
```
┌─────────────────────────────────────┐
│ [新增] [批量导入] [引用需求标准] [导出] │
├─────────────────────────────────────┤
│ 技术要求列表                         │
│ [编辑] [对比国标] [属性参考] [删除]   │
└─────────────────────────────────────┘
```

### 标的2（工程类）技术要求操作
```
┌─────────────────────────────────────┐
│ [新增] [批量导入] [导出]              │
├─────────────────────────────────────┤
│ 技术要求列表                         │
│ [编辑] [删除]                       │
└─────────────────────────────────────┘
```

## 使用场景

### 1. 工程技术要求配置
工程类项目的技术要求通常包括：

#### 工程质量要求
- 施工质量标准
- 材料质量要求
- 工艺技术要求

#### 工程安全要求
- 安全施工标准
- 安全防护措施
- 应急处理要求

#### 工程进度要求
- 施工进度安排
- 关键节点要求
- 工期保证措施

#### 工程验收要求
- 验收标准和程序
- 质量检测要求
- 竣工验收条件

### 2. 操作流程简化

#### 手动配置流程
```
点击新增 → 填写技术要求 → 保存配置
```

#### 批量导入流程
```
准备Excel模板 → 填写技术要求 → 导入系统 → 确认配置
```

#### 编辑调整流程
```
选择要求项 → 点击编辑 → 修改内容 → 保存更新
```

## 优势分析

### 1. 界面简洁
- 减少了3个操作按钮
- 界面更加简洁明了
- 用户注意力更集中

### 2. 操作高效
- 减少了不必要的操作步骤
- 提高了配置效率
- 降低了操作复杂度

### 3. 符合实际
- 更贴合工程项目的实际需求
- 避免了功能的滥用
- 提升了系统的专业性

### 4. 维护简单
- 减少了功能分支
- 降低了维护成本
- 提高了代码可读性

## 扩展考虑

### 1. 未来扩展
如果工程类项目需要特定的辅助功能，可以考虑：
- 工程规范库引用
- 工程案例参考
- 工程标准模板

### 2. 灵活配置
可以通过配置项来控制功能的显示：
```javascript
const featureConfig = {
  target1: {
    referenceStandards: true,
    standardComparison: true,
    attributeReference: true
  },
  target2: {
    referenceStandards: false,
    standardComparison: false,
    attributeReference: false
  }
}
```

## 测试要点

### 1. 功能验证
- 标的1的所有功能正常
- 标的2的简化功能正常
- 条件渲染正确执行

### 2. 界面验证
- 标的1显示完整操作按钮
- 标的2只显示核心操作按钮
- 界面布局美观协调

### 3. 交互验证
- 标的切换时按钮显示正确
- 操作流程顺畅
- 用户体验良好

## 总结

通过删除标的2技术要求配置中的"引用需求标准"、"对比国标要求"和"属性值参考"操作，实现了：

- 🎯 **专业化适配**：更符合工程类项目的实际需求
- 🔧 **界面简化**：减少不必要的操作选项，提升用户体验
- 🎨 **功能聚焦**：突出核心的技术要求配置功能
- 📊 **灵活扩展**：为不同类型项目提供差异化的功能配置
- 🚀 **高效操作**：简化操作流程，提高配置效率

这个改进使得工程类项目的技术要求配置更加专业和高效，同时保持了系统的整体一致性和可维护性。
