# 标的3基础信息维护功能说明

## 功能概述

为标的3（服务类）实现了基础信息维护弹窗功能，支持服务类项目的基础信息编辑和管理。该功能专门针对服务类项目的特点进行了优化设计，提供了符合服务采购实际需求的字段配置和验证规则。

## 功能特点

### 1. 服务类项目适配
- **项目类型**：专门针对服务类项目设计
- **字段优化**：根据服务项目特点调整字段类型和验证规则
- **行业选择**：提供服务相关的专业行业选项
- **服务特色**：突出服务期限、服务内容等服务类特有属性

### 2. 编辑弹窗设计
- **弹窗尺寸**：500px宽度，适合服务项目信息展示
- **标题明确**：显示"编辑标的3基础信息"
- **布局合理**：标签宽度140px，左对齐布局

### 3. 表单字段配置
根据服务类项目特点，配置了以下字段：

| 字段名称 | 字段类型 | 验证规则 | 说明 |
|---------|---------|---------|------|
| 标的名称 | 文本输入 | 必填，最少2字符 | 服务项目名称 |
| 采购目录 | 文本输入 | 必填 | 服务采购目录编码 |
| 最高限制单价（元） | 数字输入 | 必填，>0，2位小数 | 服务单价限制 |
| 服务期限 | 文本输入 | 必填 | 支持"1年"、"6个月"等 |
| 所属行业 | 下拉选择 | 必填 | 服务相关行业 |
| 服务内容 | 多行文本 | 必填，最少10字符 | 详细服务描述 |
| 总价（元） | 数字输入 | 必填，>0，2位小数 | 服务总价 |

## 界面设计

### 表单布局
```
┌─────────────────────────────────────┐
│ 编辑标的3基础信息                    │
├─────────────────────────────────────┤
│ 标的名称 *     [输入框]              │
│ 采购目录 *     [输入框]              │
│ 最高限制单价（元）* [数字输入框]      │
│ 服务期限 *     [输入框]              │
│ 所属行业 *     [下拉选择框]          │
│ 服务内容 *     [多行文本框]          │
│ 总价（元）*    [数字输入框]          │
│                [服务类项目提示]       │
├─────────────────────────────────────┤
│                    [取消] [保存]     │
└─────────────────────────────────────┘
```

### 服务类特色设计
- **服务期限字段**：使用文本输入，支持"1年"、"6个月"、"长期"等服务期限表述
- **行业选择**：提供物业服务、保洁服务、安保服务、餐饮服务等选项
- **服务内容**：多行文本框，支持详细描述服务内容和目标
- **总价提示**：显示"服务类项目总价通常根据服务期限和服务内容确定"

## 技术实现

### 1. 数据结构
```javascript
// 标的3编辑表单数据
const target3EditForm = ref({
  name: '',              // 标的名称
  catalog: '',           // 采购目录
  maxPrice: 0,           // 最高限制单价
  servicePeriod: '',     // 服务期限
  industry: '',          // 所属行业
  serviceContent: '',    // 服务内容
  totalPrice: 0          // 总价
})
```

### 2. 验证规则
```javascript
const target3EditFormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' },
    { min: 2, message: '标的名称至少2个字符', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请输入采购目录', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '最高限制单价必须大于0', trigger: 'blur' }
  ],
  servicePeriod: [
    { required: true, message: '请输入服务期限', trigger: 'blur' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  serviceContent: [
    { required: true, message: '请输入服务内容', trigger: 'blur' },
    { min: 10, message: '服务内容至少10个字符', trigger: 'blur' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}
```

### 3. 核心方法
```javascript
// 打开编辑抽屉
const openTarget3EditDrawer = () => {
  const currentData = target3BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))
  
  target3EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    servicePeriod: currentData.quantity, // 对应服务期限
    industry: currentData.industry,
    serviceContent: currentData.function, // 对应服务内容
    totalPrice: maxPrice
  }
  target3EditDrawerVisible.value = true
}

// 保存表单数据
const saveTarget3EditForm = () => {
  target3BasicInfo.value[0] = {
    name: target3EditForm.value.name,
    catalog: target3EditForm.value.catalog,
    maxPrice: `${target3EditForm.value.maxPrice}元`,
    quantity: target3EditForm.value.servicePeriod,
    industry: target3EditForm.value.industry,
    function: target3EditForm.value.serviceContent,
    standard: '服务质量标准'
  }
  
  ElMessage.success('标的3基础信息保存成功')
  handleTarget3DrawerClose()
}
```

## 服务类项目特点

### 1. 服务期限灵活
- **时间表述**：支持"1年"、"6个月"、"3年"等时间表述
- **长期服务**：支持"长期"、"不限"等特殊期限
- **阶段服务**：支持"第一阶段"、"试运行期"等阶段性描述

### 2. 行业分类专业
提供服务相关的专业行业选项：
- **物业服务**：物业管理、设施维护
- **保洁服务**：清洁、卫生服务
- **安保服务**：安全保卫、监控服务
- **餐饮服务**：食堂、配餐服务
- **维修服务**：设备维修、技术支持
- **咨询服务**：管理咨询、技术咨询
- **培训服务**：教育培训、技能培训
- **技术服务**：IT服务、专业技术服务
- **其他**：其他类型服务

### 3. 服务内容详细
- **多行文本**：支持详细描述服务内容
- **字数限制**：最多200字，确保描述完整但简洁
- **字数统计**：实时显示已输入字数
- **最少要求**：至少10个字符，确保描述有意义

## 与其他标的的差异

### 相同点
- 基本的表单结构和验证逻辑
- 编辑抽屉的交互方式
- 数据保存和更新机制

### 差异点
| 特性 | 标的1（设备采购） | 标的2（工程类） | 标的3（服务类） |
|------|------------------|----------------|----------------|
| 数量字段 | 数字输入+单位选择 | 文本输入（支持"不限"） | 服务期限（文本输入） |
| 行业选择 | 设备相关行业 | 工程相关行业 | 服务相关行业 |
| 特色字段 | 单位选择 | 工程量描述 | 服务内容（多行文本） |
| 总价计算 | 单价×数量 | 根据工程量清单 | 根据服务期限和内容 |
| 抽屉宽度 | 600px | 500px | 500px |

## 使用流程

### 1. 打开编辑功能
1. 在标的3表格中点击"编辑"按钮
2. 系统打开编辑抽屉
3. 自动填充当前数据到表单

### 2. 编辑基础信息
1. **标的名称**：修改服务项目名称
2. **采购目录**：更新服务采购目录编码
3. **最高限制单价**：设置服务单价上限
4. **服务期限**：输入服务期限（支持文本描述）
5. **所属行业**：选择服务相关行业
6. **服务内容**：详细描述服务内容和目标
7. **总价**：输入服务总价

### 3. 保存更新
1. 系统验证表单数据
2. 验证通过后更新数据
3. 显示保存成功提示
4. 自动关闭编辑抽屉

## 数据验证

### 必填字段验证
- 所有字段均为必填
- 空值时显示相应错误提示

### 数值字段验证
- 最高限制单价必须大于0
- 总价必须大于0
- 支持小数点后2位精度

### 文本字段验证
- 标的名称最少2个字符
- 服务内容最少10个字符
- 服务内容最多200个字符

## 数据映射

### 表单字段与数据字段的映射关系
| 表单字段 | 数据字段 | 说明 |
|---------|---------|------|
| name | name | 标的名称 |
| catalog | catalog | 采购目录 |
| maxPrice | maxPrice | 最高限制单价（加"元"后缀） |
| servicePeriod | quantity | 服务期限映射到数量字段 |
| industry | industry | 所属行业 |
| serviceContent | function | 服务内容映射到功能字段 |
| totalPrice | - | 总价（仅在表单中使用） |

## 扩展功能

### 已实现
- ✅ 完整的表单编辑功能
- ✅ 数据验证和错误提示
- ✅ 服务类项目特色适配
- ✅ 与现有系统的集成

### 待扩展
- 🔄 服务标准模板库
- 🔄 服务质量评价体系
- 🔄 服务合同条款模板
- 🔄 服务绩效指标设置

## 测试要点

### 功能测试
1. 编辑按钮点击响应
2. 表单数据正确填充
3. 各字段验证规则生效
4. 数据保存和更新正常

### 界面测试
1. 抽屉正常打开关闭
2. 表单布局美观合理
3. 字段对齐和间距正确
4. 响应式效果良好

### 数据测试
1. 数据初始化正确
2. 编辑后数据更新
3. 验证规则有效性
4. 错误处理完整性

## 总结

标的3基础信息维护功能的实现特点：

- 🎯 **专业适配**：针对服务类项目的特点进行专门设计
- 🔧 **技术复用**：基于标的1和标的2的成熟技术架构
- 🎨 **界面优化**：简洁明了的服务项目编辑界面
- 📊 **数据完整**：支持服务项目的完整信息管理
- 🚀 **易于使用**：直观的操作流程和友好的用户体验

这个功能为服务类项目的采购管理提供了专业的编辑工具，突出了服务采购的特色需求，提高了服务采购的信息管理效率和准确性。
