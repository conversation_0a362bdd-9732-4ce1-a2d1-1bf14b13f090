import { defineStore } from 'pinia'
import type { AuthState, User } from '@/types'

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    isAuthenticated: false,
    user: null,
    token: null
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    currentUser: (state) => state.user
  },

  actions: {
    // 登录
    login(user: User, token: string) {
      this.isAuthenticated = true
      this.user = user
      this.token = token
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(user))
    },

    // 登出
    logout() {
      this.isAuthenticated = false
      this.user = null
      this.token = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    },

    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('token')
      const userStr = localStorage.getItem('user')
      
      if (token && userStr) {
        try {
          const user = JSON.parse(userStr)
          this.isAuthenticated = true
          this.user = user
          this.token = token
        } catch (error) {
          console.error('Failed to parse user data:', error)
          this.logout()
        }
      }
    }
  }
})
