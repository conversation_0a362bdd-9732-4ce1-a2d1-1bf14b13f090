<template>
  <div class="procurement-file-config">
    <!-- 顶部项目信息栏 -->
    <div class="project-info-header">
      <div class="project-info-left">
        <el-icon class="info-icon"><Document /></el-icon>
        <span class="project-title">某市-分散采购-导入测试项目采购项目327225 BUYPLANNUM20230327</span>
        <el-tag type="warning" size="small" class="project-tag">分散采购</el-tag>
      </div>
      <div class="project-info-right">
        <el-button size="small">返回</el-button>
        <el-button type="primary" size="small">完成配置下一步</el-button>
      </div>
    </div>

    <!-- 主导航Tab -->
    <div class="main-tabs-section">
      <el-tabs v-model="mainActiveTab" class="main-tabs" @tab-click="handleMainTabClick">
        <el-tab-pane label="基本信息" name="basic-info">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">1</div>
              <span>基本信息</span>
              <el-icon v-if="completedTabs.includes('basic-info')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="编制流程" name="process">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">2</div>
              <span>编制流程</span>
              <el-icon v-if="completedTabs.includes('process')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="采购需求" name="requirements">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">3</div>
              <span>采购需求</span>
              <el-icon v-if="completedTabs.includes('requirements')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="资格条件及评审" name="qualification">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">4</div>
              <span>资格条件及评审</span>
              <el-icon v-if="completedTabs.includes('qualification')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="开标流程" name="bidding">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">5</div>
              <span>开标流程</span>
              <el-icon v-if="completedTabs.includes('bidding')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="评审规则" name="review-rules">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">6</div>
              <span>评审规则</span>
              <el-icon v-if="completedTabs.includes('review-rules')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane label="合同条款" name="contract">
          <template #label>
            <div class="tab-label">
              <div class="tab-circle">7</div>
              <span>合同条款</span>
              <el-icon v-if="completedTabs.includes('contract')" class="check-icon"><Check /></el-icon>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 主要内容区域 -->
    <div class="procurement-main-content">
      <!-- 根据当前选中的主Tab显示不同内容 -->

      <!-- 基本信息Tab内容 -->
      <div v-if="mainActiveTab === 'basic-info'" class="procurement-tab-content">
        <el-card class="procurement-content-card">
          <template #header>
            <div class="procurement-card-header">
              <span>基本信息配置</span>
            </div>
          </template>
          <div class="procurement-content-body">
            <p>基本信息配置内容将在这里实现...</p>
          </div>
        </el-card>
      </div>

      <!-- 编制流程Tab内容 -->
      <div v-else-if="mainActiveTab === 'process'" class="procurement-tab-content">
        <el-card class="procurement-content-card">
          <template #header>
            <div class="procurement-card-header">
              <span>编制流程配置</span>
            </div>
          </template>
          <div class="procurement-content-body">
            <p>编制流程配置内容将在这里实现...</p>
          </div>
        </el-card>
      </div>

      <!-- 采购需求Tab内容 -->
      <div v-else-if="mainActiveTab === 'requirements'" class="procurement-tab-content">
        <div class="procurement-requirements-section">
          <!-- 标项切换Tab -->
          <el-tabs v-model="requirementsActiveTab" class="procurement-requirements-tabs">
            <el-tab-pane label="标项1 （项目采购）" name="item1">
              <!-- 标的列表 -->
              <div class="procurement-targets-list">
                <!-- 标的1 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的1 (项目采购-货物类)</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target1BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small" @click="openTarget1EditDrawer">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target1Requirements.policy.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target1Requirements.policy.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget1Policy">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget1Policy">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">技术要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target1Requirements.technical.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target1Requirements.technical.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget1Technical">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget1Technical">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target1Requirements.business.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target1Requirements.business.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget1Business">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget1Business">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的2 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的2（项目采购-工程类）</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target2BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>

                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small" @click="openTarget2EditDrawer">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target2Requirements.policy.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target2Requirements.policy.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget2Policy">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget2Policy">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">技术要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target2Requirements.technical.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target2Requirements.technical.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget2Technical">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget2Technical">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target2Requirements.business.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target2Requirements.business.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget2Business">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget2Business">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的3 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的3（物业服务类）</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target3BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small" @click="openTarget3EditDrawer">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">服务要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target3Requirements.service.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target3Requirements.service.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget3Service">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget3Service">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target3Requirements.business.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target3Requirements.business.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget3Business">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget3Business">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的4 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的4（服务类-非物业）</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target4BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业"  align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small" @click="openTarget4EditDrawer">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target4Requirements.policy.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target4Requirements.policy.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget4Policy">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget4Policy">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">技术要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target4Requirements.technical.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target4Requirements.technical.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget4Technical">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget4Technical">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <!-- 已配置状态 -->
                          <div v-if="target4Requirements.business.configured" class="requirement-configured">
                            <div class="configured-info">
                              <el-icon class="configured-icon"><Check /></el-icon>
                              <span class="configured-text">已配置{{ target4Requirements.business.count }}条数据</span>
                            </div>
                            <el-button type="primary" link size="small" @click="editTarget4Business">编辑配置</el-button>
                          </div>
                          <!-- 未配置状态 -->
                          <div v-else class="requirement-unconfigured">
                            <div class="unconfigured-info">
                              <el-icon class="unconfigured-icon"><QuestionFilled /></el-icon>
                              <span class="unconfigured-text">暂未配置</span>
                            </div>
                            <el-button type="primary" @click="configureTarget4Business">点击配置</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的5 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的5</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target5BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价" width="140" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业" width="100" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="function" label="功能或目标" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.function }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="standard" label="标准或规范" width="120" align="center">
                        <template #default="scope">
                          {{ scope.row.standard }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <p>政策要求内容...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">服务要求</div>
                        <div class="procurement-requirement-content">
                          <p>技术规格要求...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <p>商务条件要求...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的6 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的6</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target6BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价" width="140" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业" width="100" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="function" label="功能或目标" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.function }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="standard" label="标准或规范" width="120" align="center">
                        <template #default="scope">
                          {{ scope.row.standard }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <p>政策要求内容...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">技术要求</div>
                        <div class="procurement-requirement-content">
                          <p>技术规格要求...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <p>商务条件要求...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 标的7 -->
                <div class="procurement-target-item">
                  <div class="procurement-target-header">
                    <h3>标的7</h3>
                  </div>

                  <!-- 标的基础信息表格 -->
                  <div class="procurement-target-basic-info">
                    <el-table :data="target7BasicInfo" border class="procurement-info-table">
                      <el-table-column prop="name" label="标的名称" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.name }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="catalog" label="采购目录" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.catalog }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="maxPrice" label="最高限制单价" width="140" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.maxPrice }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="quantity" label="数量及单位" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.quantity }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="industry" label="所属行业" width="100" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.industry }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="function" label="功能或目标" width="120" align="center">
                        <template #default="scope">
                          <span class="required-field">{{ scope.row.function }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="standard" label="标准或规范" width="120" align="center">
                        <template #default="scope">
                          {{ scope.row.standard }}
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center">
                        <template #default>
                          <el-button type="primary" link size="small">编辑</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 标的详细要求 -->
                  <div class="procurement-target-requirements">
                    <div class="procurement-requirements-grid">
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">落实政府采购政策</div>
                        <div class="procurement-requirement-content">
                          <p>政策要求内容...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">技术要求</div>
                        <div class="procurement-requirement-content">
                          <p>技术规格要求...</p>
                        </div>
                      </div>
                      <div class="procurement-requirement-card">
                        <div class="procurement-requirement-header">商务要求</div>
                        <div class="procurement-requirement-content">
                          <p>商务条件要求...</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="标项2 （框架协议-货物类）" name="item2">
              <div class="procurement-targets-list">
                <div class="procurement-empty-state">
                  <p>标项2的内容待配置...</p>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="标项3 （框架协议-服务类）" name="item3">
              <div class="procurement-targets-list">
                <div class="procurement-empty-state">
                  <p>标项3的内容待配置...</p>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="标项4" name="item4">
              <div class="procurement-targets-list">
                <div class="procurement-empty-state">
                  <p>标项4的内容待配置...</p>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 资格条件及评审Tab内容 -->
      <div v-else-if="mainActiveTab === 'qualification'" class="procurement-tab-content">
        <el-card class="procurement-content-card">
          <template #header>
            <div class="procurement-card-header">
              <span>资格条件及评审配置</span>
            </div>
          </template>
          <div class="procurement-content-body">
            <p>资格条件及评审配置内容将在这里实现...</p>
          </div>
        </el-card>
      </div>

      <!-- 开标流程Tab内容 -->
      <div v-else-if="mainActiveTab === 'bidding'" class="procurement-tab-content">
        <el-card class="procurement-content-card">
          <template #header>
            <div class="procurement-card-header">
              <span>开标流程配置</span>
            </div>
          </template>
          <div class="procurement-content-body">
            <p>开标流程配置内容将在这里实现...</p>
          </div>
        </el-card>
      </div>

      <!-- 评审规则Tab内容（当前显示的内容） -->
      <div v-else-if="mainActiveTab === 'review-rules'" class="procurement-tab-content">
        <!-- 基础信息区域 -->
        <div class="procurement-basic-info-section">
          <el-card class="procurement-basic-info-card">
            <template #header>
              <div class="procurement-card-header">
                <span>基础信息</span>
              </div>
            </template>
            <div class="procurement-basic-info-content">
              <div class="procurement-info-item">
                <span class="procurement-label">合同1</span>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 分包信息Tab区域 -->
        <div class="procurement-package-tabs-section">
          <el-tabs v-model="activeTab" type="card" class="procurement-package-tabs">
            <el-tab-pane label="分包合同 R1" name="package1">
              <!-- 分包基本信息展示 -->
              <div class="procurement-package-info-display">
                <el-card class="procurement-package-info-card">
                  <div class="procurement-package-info-grid">
                    <div class="procurement-info-row">
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">分包编号</span>
                        <span class="procurement-info-value">1</span>
                      </div>
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">配置类型</span>
                        <span class="procurement-info-value">投标</span>
                      </div>
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">配置金额（元）</span>
                        <span class="procurement-info-value">300,000.00</span>
                        <span class="procurement-info-note">最高限价</span>
                      </div>
                    </div>
                    <div class="procurement-info-row">
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">采购单位</span>
                        <span class="procurement-info-value">吉林市水务集团有限公司</span>
                      </div>
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">预算（万元）</span>
                        <span class="procurement-info-value">-</span>
                      </div>
                      <div class="procurement-info-item">
                        <span class="procurement-info-label">合同履约期限</span>
                        <span class="procurement-info-value">12</span>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>

              <!-- 配置表单区域 -->
              <div class="procurement-config-form-section">
                <el-tabs v-model="configActiveTab" class="procurement-config-tabs">
                  <el-tab-pane label="基本配置" name="basic">
                    <el-card class="procurement-config-card">
                      <div class="procurement-form-content">
                        <!-- 基本信息配置 -->
                        <div class="procurement-form-row">
                          <div class="procurement-form-item">
                            <label class="procurement-required">最高限价（元）</label>
                            <el-input
                              v-model="maxPrice"
                              placeholder="300,000.00"
                              class="procurement-price-input"
                            />
                            <el-icon class="procurement-help-icon"><QuestionFilled /></el-icon>
                          </div>
                          <div class="procurement-form-item">
                            <label class="procurement-required">合同履约期限</label>
                            <el-input
                              v-model="contractPeriod"
                              placeholder="12"
                              class="procurement-period-input"
                            />
                            <span class="procurement-unit-text">2/400</span>
                          </div>
                        </div>

                        <div class="procurement-form-row">
                          <div class="procurement-form-item procurement-full-width">
                            <label>采购需求描述</label>
                            <el-input
                              v-model="procurementDesc"
                              type="textarea"
                              :rows="3"
                              placeholder="满足"
                              class="procurement-desc-textarea"
                            />
                            <span class="procurement-char-count">2/4000</span>
                          </div>
                          <div class="procurement-form-item">
                            <label>备注</label>
                            <el-input
                              v-model="remark"
                              type="textarea"
                              :rows="3"
                              placeholder="请输入"
                              class="procurement-remark-textarea"
                            />
                            <span class="procurement-char-count">0/400</span>
                          </div>
                        </div>

                        <!-- 是否采用中小企业政策 -->
                        <div class="procurement-form-row">
                          <div class="procurement-form-item">
                            <label>是否采用中小企业政策</label>
                            <el-select v-model="smePolicy" placeholder="是否">
                              <el-option label="是" value="yes" />
                              <el-option label="否" value="no" />
                            </el-select>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-tab-pane>

                  <el-tab-pane label="流标文件管理" name="file-management">
                    <el-card class="procurement-config-card">
                      <div class="procurement-form-content">
                        <!-- 流标文件管理 -->
                        <div class="form-row">
                          <div class="form-item">
                            <label>流标文件有效起始时间</label>
                            <el-date-picker
                              v-model="startDate"
                              type="date"
                              placeholder="2025-07-23"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
                            />
                          </div>
                          <div class="form-item">
                            <label>流标文件有效截止时间</label>
                            <el-date-picker
                              v-model="endDate"
                              type="datetime"
                              placeholder="2025-07-30 20:57"
                              format="YYYY-MM-DD HH:mm"
                              value-format="YYYY-MM-DD HH:mm"
                            />
                          </div>
                        </div>

                        <div class="form-row">
                          <div class="form-item">
                            <label>流标文件提交上午时间</label>
                            <el-time-picker
                              v-model="morningTime"
                              placeholder="00:00:00"
                              format="HH:mm:ss"
                              value-format="HH:mm:ss"
                            />
                            <span class="time-separator">-</span>
                            <el-time-picker
                              v-model="morningEndTime"
                              placeholder="12:00:00"
                              format="HH:mm:ss"
                              value-format="HH:mm:ss"
                            />
                          </div>
                          <div class="form-item">
                            <label>流标文件提交下午时间</label>
                            <el-time-picker
                              v-model="afternoonTime"
                              placeholder="12:00:00"
                              format="HH:mm:ss"
                              value-format="HH:mm:ss"
                            />
                            <span class="time-separator">-</span>
                            <el-time-picker
                              v-model="afternoonEndTime"
                              placeholder="23:59:59"
                              format="HH:mm:ss"
                              value-format="HH:mm:ss"
                            />
                          </div>
                        </div>

                        <!-- 获取文件方式 -->
                        <div class="form-row">
                          <div class="form-item">
                            <label>获取文件</label>
                            <span>需要采购文件在线获取文件</span>
                          </div>
                        </div>

                        <!-- 获取文件方式说明 -->
                        <div class="form-row">
                          <div class="form-item full-width">
                            <label>获取文件方式</label>
                            <el-input
                              v-model="fileMethod"
                              type="textarea"
                              :rows="2"
                              placeholder="请输入获取文件方式说明"
                              class="method-textarea"
                            />
                            <span class="char-count">63/400</span>
                          </div>
                        </div>

                        <!-- 采购文件售价 -->
                        <div class="form-row">
                          <div class="form-item">
                            <label>采购文件售价（元）</label>
                            <el-input
                              v-model="filePrice"
                              placeholder="不收费"
                              class="price-input"
                            />
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 合同条款Tab内容 -->
      <div v-else-if="mainActiveTab === 'contract'" class="procurement-tab-content">
        <el-card class="procurement-content-card">
          <template #header>
            <div class="procurement-card-header">
              <span>合同条款配置</span>
            </div>
          </template>
          <div class="procurement-content-body">
            <p>合同条款配置内容将在这里实现...</p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 政府采购政策编辑抽屉 -->
    <el-drawer
      v-model="policyEditDrawerVisible"
      title="编辑落实政府采购政策"
      direction="rtl"
      size="800px"
      :before-close="handlePolicyDrawerClose"
    >
      <div class="policy-edit-container">
        <div class="policy-list-header">
          <span class="policy-count">共{{ policyList.length }}条政策配置</span>
          <el-button type="primary" @click="addPolicyItem">
            <el-icon><Plus /></el-icon>
            添加政策
          </el-button>
        </div>

        <div class="policy-list">
          <el-table :data="policyList" border class="policy-table">
            <el-table-column type="index" label="序号" width="60" align="center" />

            <el-table-column label="政府采购政策" min-width="180">
              <template #default="scope">
                <el-select
                  v-model="scope.row.policyType"
                  placeholder="请选择政府采购政策"
                  clearable
                  size="small"
                  style="width: 100%"
                >
                  <el-option label="促进中小企业发展" value="sme_development" />
                  <el-option label="支持监狱企业发展" value="prison_enterprise" />
                  <el-option label="促进残疾人就业" value="disabled_employment" />
                  <el-option label="信息安全产品" value="information_security" />
                  <el-option label="节能环保产品" value="energy_saving" />
                  <el-option label="自主创新产品" value="innovation_product" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="落实要求" min-width="120">
              <template #default="scope">
                <el-checkbox-group v-model="scope.row.requirements" size="small">
                  <el-checkbox value="mandatory">强制</el-checkbox>
                  <el-checkbox value="priority">优先</el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>

            <el-table-column label="实质性响应要求" width="140" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isSubstantive" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="removePolicyItem(scope.$index)"
                  :disabled="policyList.length <= 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="policy-drawer-footer">
          <el-button @click="handlePolicyDrawerClose">取消</el-button>
          <el-button type="primary" @click="savePolicyConfig">保存配置</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 商务要求编辑抽屉 -->
    <el-drawer
      v-model="businessEditDrawerVisible"
      title="编辑商务要求"
      direction="rtl"
      size="800px"
      :before-close="handleBusinessDrawerClose"
    >
      <div class="business-edit-container">
        <div class="business-list-header">
          <span class="business-count">共{{ businessList.length }}条商务要求</span>
          <el-button type="primary" @click="addBusinessItem">
            <el-icon><Plus /></el-icon>
            添加要求
          </el-button>
        </div>

        <div class="business-list">
          <el-table :data="businessList" border class="business-table">
            <el-table-column type="index" label="序号" width="60" align="center" />

            <el-table-column label="类型" width="120">
              <template #default="scope">
                <el-select
                  v-model="scope.row.type"
                  placeholder="请选择类型"
                  clearable
                  size="small"
                  style="width: 100%"
                >
                  <el-option label="资质要求" value="qualification" />
                  <el-option label="业绩要求" value="performance" />
                  <el-option label="人员要求" value="personnel" />
                  <el-option label="设备要求" value="equipment" />
                  <el-option label="财务要求" value="financial" />
                  <el-option label="服务要求" value="service" />
                  <el-option label="交付要求" value="delivery" />
                  <el-option label="质量要求" value="quality" />
                  <el-option label="其他要求" value="other" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="要求内容" min-width="300">
              <template #default="scope">
                <el-input
                  v-model="scope.row.content"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入具体的商务要求内容"
                  maxlength="500"
                  show-word-limit
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="评审因素" width="100" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isReviewFactor" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="实质性响应" width="110" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isSubstantive" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="removeBusinessItem(scope.$index)"
                  :disabled="businessList.length <= 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="business-drawer-footer">
          <el-button @click="handleBusinessDrawerClose">取消</el-button>
          <el-button type="primary" @click="saveBusinessConfig">保存配置</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 技术要求编辑抽屉 -->
    <el-drawer
      v-model="technicalEditDrawerVisible"
      title="编辑技术要求"
      direction="rtl"
      size="900px"
      :before-close="handleTechnicalDrawerClose"
    >
      <div class="technical-edit-container">
        <div class="technical-list-header">
          <span class="technical-count">共{{ technicalList.length }}条技术要求</span>
          <div class="technical-header-actions">
            <el-button type="success" plain size="small" @click="importTechnicalRequirements">
              <el-icon><Upload /></el-icon>
              导入技术要求
            </el-button>
            <el-button type="info" plain size="small" @click="exportTechnicalRequirements">
              <el-icon><Download /></el-icon>
              导出技术要求
            </el-button>
            <el-button
              v-if="currentEditingTarget === 'target1'"
              type="warning"
              plain
              size="small"
              @click="referenceStandards"
            >
              <el-icon><Document /></el-icon>
              引用需求标准
            </el-button>
            <el-button type="primary" plain size="small" @click="editCategories">
              <el-icon><Edit /></el-icon>
              编辑分类
            </el-button>
            <el-button type="primary" @click="addTechnicalItem">
              <el-icon><Plus /></el-icon>
              添加要求
            </el-button>
          </div>
        </div>

        <div class="technical-list">
          <el-table :data="technicalList" border class="technical-table">
            <el-table-column type="index" label="序号" width="60" align="center" />

            <el-table-column label="分类" width="120">
              <template #default="scope">
                <el-select
                  v-model="scope.row.category"
                  placeholder="请选择分类"
                  filterable
                  allow-create
                  clearable
                  size="small"
                  style="width: 100%"
                >
                  <el-option
                    v-for="category in categoryList"
                    :key="category.name"
                    :label="category.name"
                    :value="category.name"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="指标名称" min-width="200">
              <template #default="scope">
                <div class="indicator-name-container">
                  <div class="indicator-mode-switch">
                    <el-radio-group v-model="scope.row.indicatorMode" size="small">
                      <el-radio-button value="single">单级</el-radio-button>
                      <el-radio-button value="hierarchical">二级</el-radio-button>
                    </el-radio-group>
                  </div>

                  <!-- 单级指标模式 -->
                  <div v-if="scope.row.indicatorMode === 'single'" class="single-indicator">
                    <el-input
                      v-model="scope.row.indicatorName"
                      placeholder="请输入指标名称"
                      clearable
                      size="small"
                    />
                  </div>

                  <!-- 二级指标模式 -->
                  <div v-else class="hierarchical-indicator">
                    <div class="indicator-level-row">
                      <el-input
                        v-model="scope.row.primaryIndicator"
                        placeholder="一级指标"
                        clearable
                        size="small"
                        class="primary-indicator"
                      />
                      <span class="indicator-separator">/</span>
                      <el-input
                        v-model="scope.row.secondaryIndicator"
                        placeholder="二级指标"
                        clearable
                        size="small"
                        class="secondary-indicator"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="指标要求" min-width="300">
              <template #default="scope">
                <el-input
                  v-model="scope.row.requirement"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入详细的技术指标要求"
                  maxlength="800"
                  show-word-limit
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="评审因素" width="100" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isReviewFactor" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="实质性响应" width="110" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isSubstantive" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" align="center">
              <template #default="scope">
                <div class="technical-actions">
                  <el-button
                    v-if="currentEditingTarget === 'target1'"
                    type="primary"
                    link
                    size="small"
                    @click="openStandardComparisonDialog(scope.row, scope.$index)"
                  >
                    对比国标要求
                  </el-button>
                  <el-button
                    v-if="currentEditingTarget === 'target1'"
                    type="info"
                    link
                    size="small"
                    @click="openAttributeReferenceDialog(scope.row, scope.$index)"
                  >
                    属性值参考
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="removeTechnicalItem(scope.$index)"
                    :disabled="technicalList.length <= 1"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="technical-drawer-footer">
          <el-button @click="handleTechnicalDrawerClose">取消</el-button>
          <el-button type="primary" @click="saveTechnicalConfig">保存配置</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 服务要求编辑抽屉 -->
    <el-drawer
      v-model="serviceEditDrawerVisible"
      title="编辑服务要求"
      direction="rtl"
      size="900px"
      :before-close="handleServiceDrawerClose"
    >
      <div class="service-edit-container">
        <div class="service-list-header">
          <span class="service-count">共{{ serviceList.length }}条服务要求</span>
          <div class="service-header-actions">
            <el-button type="success" plain size="small" @click="importServiceRequirements">
              <el-icon><Upload /></el-icon>
              导入服务要求
            </el-button>
            <el-button type="info" plain size="small" @click="exportServiceRequirements">
              <el-icon><Download /></el-icon>
              导出服务要求
            </el-button>
            <el-button
              v-if="currentEditingTarget === 'target3'"
              type="warning"
              plain
              size="small"
              @click="referenceServiceStandards"
            >
              <el-icon><Document /></el-icon>
              引用需求标准
            </el-button>
            <el-button type="primary" @click="addServiceRequirementItem">
              <el-icon><Plus /></el-icon>
              新增服务要求
            </el-button>
          </div>
        </div>

        <div class="service-list-content">
          <el-table :data="serviceList" border style="width: 100%" class="service-table">
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>

            <el-table-column label="服务分类" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.category" placeholder="选择分类" size="small">
                  <el-option label="服务质量" value="quality" />
                  <el-option label="服务标准" value="standard" />
                  <el-option label="服务时间" value="time" />
                  <el-option label="服务人员" value="personnel" />
                  <el-option label="服务设备" value="equipment" />
                  <el-option label="服务流程" value="process" />
                  <el-option label="应急响应" value="emergency" />
                  <el-option label="引用标准" value="reference" />
                  <el-option label="其他要求" value="other" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column label="服务要求名称" min-width="150">
              <template #default="scope">
                <el-input
                  v-model="scope.row.serviceName"
                  placeholder="请输入服务要求名称"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="具体要求" min-width="250">
              <template #default="scope">
                <el-input
                  v-model="scope.row.requirement"
                  type="textarea"
                  :rows="2"
                  placeholder="请详细描述服务要求"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="评审因素" width="100" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isReviewFactor" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="实质性响应" width="110" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.isSubstantive" size="small">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" align="center">
              <template #default="scope">
                <div class="service-actions">
                  <el-button
                    v-if="currentEditingTarget === 'target3'"
                    type="primary"
                    link
                    size="small"
                    @click="openServiceStandardComparisonDialog(scope.row, scope.$index)"
                  >
                    对比国标要求
                  </el-button>
                  <el-button
                    v-if="currentEditingTarget === 'target3'"
                    type="info"
                    link
                    size="small"
                    @click="openServiceAttributeReferenceDialog(scope.row, scope.$index)"
                  >
                    属性值参考
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="removeServiceRequirementItem(scope.$index)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="service-drawer-footer">
          <el-button @click="handleServiceDrawerClose">取消</el-button>
          <el-button type="primary" @click="saveServiceConfig">保存配置</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="categoryEditDialogVisible"
      title="编辑技术要求分类"
      width="600px"
      :before-close="handleCategoryDialogClose"
    >
      <div class="category-edit-container">
        <div class="category-list-header">
          <span class="category-count">共{{ categoryList.length }}个分类</span>
          <el-button type="primary" size="small" @click="addCategory">
            <el-icon><Plus /></el-icon>
            添加分类
          </el-button>
        </div>

        <div class="category-list">
          <div
            v-for="(category, index) in categoryList"
            :key="index"
            class="category-item"
          >
            <el-input
              v-model="category.name"
              placeholder="请输入分类名称"
              clearable
              class="category-input"
            />
            <el-input
              v-model="category.description"
              placeholder="请输入分类描述（可选）"
              clearable
              class="category-description"
            />
            <el-button
              type="danger"
              link
              size="small"
              @click="removeCategory(index)"
              :disabled="categoryList.length <= 1"
              class="category-delete-btn"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="category-preset-section">
          <div class="preset-header">
            <span>预设分类</span>
            <el-button type="text" size="small" @click="resetToPresetCategories">
              恢复默认分类
            </el-button>
          </div>
          <div class="preset-categories">
            <el-tag
              v-for="preset in presetCategories"
              :key="preset.value"
              class="preset-tag"
              @click="addPresetCategory(preset)"
            >
              {{ preset.label }}
            </el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCategoryDialogClose">取消</el-button>
          <el-button type="primary" @click="saveCategoryConfig">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 对表国标要求弹窗 -->
    <el-dialog
      v-model="standardComparisonDialogVisible"
      title="对比国标要求"
      width="1000px"
      :before-close="handleStandardComparisonDialogClose"
    >
      <div class="standard-comparison-container">
        <div class="comparison-header">
          <div class="current-requirement">
            <h4>当前配置要求</h4>
            <div class="requirement-info">
              <div class="info-item">
                <span class="label">分类：</span>
                <span class="value">{{ currentTechnicalItem?.category || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ currentEditingTarget === 'target3' ? '服务要求名称：' : '指标名称：' }}</span>
                <span class="value">
                  {{ currentEditingTarget === 'target3'
                      ? currentTechnicalItem?.serviceName
                      : (currentTechnicalItem?.indicatorMode === 'single'
                          ? currentTechnicalItem?.indicatorName
                          : `${currentTechnicalItem?.primaryIndicator}/${currentTechnicalItem?.secondaryIndicator}`) }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">指标要求：</span>
                <span class="value">{{ currentTechnicalItem?.requirement || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="comparison-content">
          <el-table :data="currentStandardComparisonList" border class="comparison-table">
            <el-table-column label="国标编号" width="120" align="center">
              <template #default="scope">
                <span class="standard-code">{{ scope.row.standardCode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="国标名称" min-width="200">
              <template #default="scope">
                <span class="standard-name">{{ scope.row.standardName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="国标要求" min-width="300">
              <template #default="scope">
                <span class="standard-requirement">{{ scope.row.standardRequirement }}</span>
              </template>
            </el-table-column>
            <el-table-column label="差异分析" min-width="250">
              <template #default="scope">
                <div class="difference-analysis">
                  <el-tag
                    :type="scope.row.differenceLevel === 'none' ? 'success' :
                           scope.row.differenceLevel === 'minor' ? 'warning' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.differenceLevel === 'none' ? '无差异' :
                       scope.row.differenceLevel === 'minor' ? '轻微差异' : '重大差异' }}
                  </el-tag>
                  <p class="difference-desc">{{ scope.row.differenceDescription }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="建议" min-width="200">
              <template #default="scope">
                <span class="suggestion">{{ scope.row.suggestion }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="comparison-footer">
          <el-button @click="handleStandardComparisonDialogClose">关闭</el-button>
          <el-button type="primary" @click="applyStandardRequirement">应用国标要求</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 引用需求标准弹窗 -->
    <el-dialog
      v-model="referenceStandardDialogVisible"
      title="引用需求标准"
      width="1200px"
      :before-close="handleReferenceStandardDialogClose"
      class="reference-standard-dialog"
    >
      <div class="reference-standard-container">
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-row">
            <div class="search-item">
              <label>需求标准名称：</label>
              <el-input
                v-model="standardSearchForm.standardName"
                placeholder="请输入"
                clearable
                class="search-input"
              />
            </div>
            <div class="search-item">
              <label>采购目录：</label>
              <el-select
                v-model="standardSearchForm.procurementCategory"
                placeholder="请选择"
                clearable
                class="search-select"
              >
                <el-option label="数据库政府采购需求标准" value="database" />
                <el-option label="操作系统政府采购需求标准" value="os" />
                <el-option label="网络设备政府采购需求标准" value="network" />
                <el-option label="安全设备政府采购需求标准" value="security" />
              </el-select>
            </div>
            <div class="search-item">
              <label>指标名称：</label>
              <el-input
                v-model="standardSearchForm.indicatorName"
                placeholder="请输入"
                clearable
                class="search-input"
              />
            </div>
            <div class="search-actions">
              <el-button @click="resetStandardSearch">重置</el-button>
              <el-button type="primary" @click="searchStandards">搜索</el-button>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content-section">
          <!-- 左侧采购目录树 -->
          <div class="left-sidebar">
            <div class="sidebar-header">
              <h4>采购目录</h4>
            </div>
            <div class="category-list">
              <div
                v-for="category in procurementCategories"
                :key="category.id"
                class="category-item"
                :class="{ active: selectedCategoryId === category.id }"
                @click="selectCategory(category.id)"
              >
                <div class="category-info">
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-year">{{ category.year }}</div>
                  <div class="category-code">{{ category.code }}</div>
                </div>
                <div class="category-count">已选 {{ getSelectedCount(category.id) }} 条</div>
              </div>
            </div>
          </div>

          <!-- 右侧详细数据 -->
          <div class="right-content">
            <div class="content-header">
              <div class="selected-category-info">
                <h4>{{ getCurrentCategoryName() }}</h4>
                <span class="total-count">共 {{ currentStandardList.length }} 条</span>
              </div>
              <div class="batch-actions">
                <el-button size="small" @click="selectAllCurrentPage">全选当页</el-button>
                <el-button size="small" @click="clearAllSelections">清空选择</el-button>
              </div>
            </div>

            <div class="standards-table-container">
              <el-table
                :data="currentStandardList"
                border
                class="standards-table"
                @selection-change="handleStandardSelectionChange"
                max-height="400"
              >
                <el-table-column type="selection" width="50" align="center" />

                <el-table-column label="指标分类" width="100" align="center">
                  <template #default="scope">
                    <el-tag
                      :type="getIndicatorCategoryType(scope.row.indicatorCategory)"
                      size="small"
                      class="category-tag"
                    >
                      {{ scope.row.indicatorCategory }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="指标名称" min-width="120">
                  <template #default="scope">
                    <div class="indicator-info">
                      <div class="primary-indicator">{{ scope.row.primaryIndicator }}</div>
                      <div class="secondary-indicator">{{ scope.row.secondaryIndicator }}</div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="指标要求" min-width="200">
                  <template #default="scope">
                    <div class="requirement-text">{{ scope.row.requirement }}</div>
                  </template>
                </el-table-column>

                <el-table-column label="使用说明" min-width="150">
                  <template #default="scope">
                    <div class="usage-description">{{ scope.row.usageDescription }}</div>
                  </template>
                </el-table-column>

                <el-table-column label="其他属性" min-width="120">
                  <template #default="scope">
                    <div class="other-attributes">
                      <el-tag
                        v-for="attr in scope.row.otherAttributes"
                        :key="attr"
                        size="small"
                        class="attribute-tag"
                      >
                        {{ attr }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="dialog-footer">
          <div class="selected-info">
            已选择 {{ selectedStandards.length }} 条需求标准
          </div>
          <div class="footer-actions">
            <el-button @click="handleReferenceStandardDialogClose">取消</el-button>
            <el-button type="primary" @click="applySelectedStandards" :disabled="selectedStandards.length === 0">
              引用
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 属性值参考弹窗 -->
    <el-dialog
      v-model="attributeReferenceDialogVisible"
      title="属性值参考"
      width="800px"
      :before-close="handleAttributeReferenceDialogClose"
    >
      <div class="attribute-reference-container">
        <div class="reference-header">
          <div class="current-indicator">
            <h4>当前指标信息</h4>
            <div class="indicator-info">
              <div class="info-item">
                <span class="label">{{ currentEditingTarget === 'target3' ? '服务要求名称：' : '指标名称：' }}</span>
                <span class="value">
                  {{ currentEditingTarget === 'target3'
                      ? currentTechnicalItem?.serviceName
                      : (currentTechnicalItem?.indicatorMode === 'single'
                          ? currentTechnicalItem?.indicatorName
                          : `${currentTechnicalItem?.primaryIndicator}/${currentTechnicalItem?.secondaryIndicator}`) }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">当前要求：</span>
                <span class="value">{{ currentTechnicalItem?.requirement || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="reference-content">
          <div class="reference-tabs">
            <el-tabs v-model="attributeReferenceActiveTab" type="card">
              <el-tab-pane label="常用参数值" name="common">
                <div class="common-values">
                  <div class="value-category" v-for="category in commonAttributeValues" :key="category.name">
                    <h5>{{ category.name }}</h5>
                    <div class="value-list">
                      <el-tag
                        v-for="value in category.values"
                        :key="value.id"
                        class="value-tag"
                        @click="selectAttributeValue(value.text)"
                        style="cursor: pointer; margin: 4px;"
                      >
                        {{ value.text }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="行业标准值" name="industry">
                <div class="industry-standards">
                  <el-table :data="industryStandardValues" border class="industry-table">
                    <el-table-column label="行业" width="120" align="center">
                      <template #default="scope">
                        <span>{{ scope.row.industry }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="标准值" min-width="200">
                      <template #default="scope">
                        <span>{{ scope.row.standardValue }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="说明" min-width="150">
                      <template #default="scope">
                        <span>{{ scope.row.description }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="scope">
                        <el-button
                          type="primary"
                          link
                          size="small"
                          @click="selectAttributeValue(scope.row.standardValue)"
                        >
                          选择
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane label="历史配置" name="history">
                <div class="history-values">
                  <el-table :data="historyAttributeValues" border class="history-table">
                    <el-table-column label="项目名称" min-width="150">
                      <template #default="scope">
                        <span>{{ scope.row.projectName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="配置值" min-width="200">
                      <template #default="scope">
                        <span>{{ scope.row.configValue }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="配置时间" width="120" align="center">
                      <template #default="scope">
                        <span>{{ scope.row.configTime }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="scope">
                        <el-button
                          type="primary"
                          link
                          size="small"
                          @click="selectAttributeValue(scope.row.configValue)"
                        >
                          选择
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <div class="reference-footer">
          <el-button @click="handleAttributeReferenceDialogClose">关闭</el-button>
          <el-button type="primary" @click="applySelectedAttributeValue">应用选择的值</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 标的1编辑抽屉 -->
    <el-drawer
      v-model="target1EditDrawerVisible"
      title="编辑标的基础信息"
      direction="rtl"
      size="700px"
      :before-close="handleTarget1DrawerClose"
    >
      <div class="target-edit-form">
        <el-form
          ref="target1FormRef"
          :model="target1EditForm"
          :rules="target1FormRules"
          label-width="140px"
          label-position="left"
        >
          <el-form-item label="标的名称" prop="name" required>
            <el-input
              v-model="target1EditForm.name"
              placeholder="请输入标的名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="采购目录" prop="catalog" required>
            <el-select
              v-model="target1EditForm.catalog"
              placeholder="请选择采购目录"
              clearable
              style="width: 100%"
            >
              <el-option label="计算机设备" value="计算机设备" />
              <el-option label="网络设备" value="网络设备" />
              <el-option label="存储设备" value="存储设备" />
              <el-option label="安全设备" value="安全设备" />
              <el-option label="办公设备" value="办公设备" />
              <el-option label="其他设备" value="其他设备" />
            </el-select>
          </el-form-item>

          <el-form-item label="最高限制单价（元）" prop="maxPrice" required>
            <el-input-number
              v-model="target1EditForm.maxPrice"
              :min="0"
              :precision="2"
              placeholder="请输入最高限制单价"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="数量" prop="quantity" required>
            <el-input-number
              v-model="target1EditForm.quantity"
              :min="1"
              placeholder="请输入数量"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="单位" prop="unit" required>
            <el-select
              v-model="target1EditForm.unit"
              placeholder="请选择单位"
              clearable
              style="width: 100%"
            >
              <el-option label="台" value="台" />
              <el-option label="套" value="套" />
              <el-option label="个" value="个" />
              <el-option label="件" value="件" />
              <el-option label="批" value="批" />
              <el-option label="项" value="项" />
            </el-select>
          </el-form-item>

          <el-form-item label="所属行业" prop="industry" required>
            <el-select
              v-model="target1EditForm.industry"
              placeholder="请选择所属行业"
              clearable
              style="width: 100%"
            >
              <el-option label="计算机" value="计算机" />
              <el-option label="网络通信" value="网络通信" />
              <el-option label="存储系统" value="存储系统" />
              <el-option label="网络安全" value="网络安全" />
              <el-option label="安防监控" value="安防监控" />
              <el-option label="办公设备" value="办公设备" />
              <el-option label="显示设备" value="显示设备" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>

          <el-form-item label="总价（元）" prop="totalPrice" required>
            <el-input-number
              v-model="target1EditForm.totalPrice"
              :min="0"
              :precision="2"
              placeholder="请输入总价"
              style="width: 100%"
              @change="handleTotalPriceChange"
            />
            <div class="price-calculation-hint">
              <span class="hint-text">
                建议总价 = 最高限制单价 × 数量 = {{ target1EditForm.maxPrice }} × {{ target1EditForm.quantity }} = {{ suggestedTotalPrice }} 元
              </span>
            </div>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleTarget1DrawerClose">取消</el-button>
          <el-button type="primary" @click="saveTarget1Info">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 标的2编辑抽屉 -->
    <el-drawer
      v-model="target2EditDrawerVisible"
      title="编辑标的2基础信息"
      direction="rtl"
      size="650px"
      :before-close="handleTarget2DrawerClose"
    >
      <div class="target-edit-form">
        <el-form
          ref="target2EditFormRef"
          :model="target2EditForm"
          :rules="target2EditFormRules"
          label-width="140px"
          label-position="left"
        >
          <el-form-item label="标的名称" prop="name" required>
            <el-input
              v-model="target2EditForm.name"
              placeholder="请输入标的名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="采购目录" prop="catalog" required>
            <el-input
              v-model="target2EditForm.catalog"
              placeholder="请输入采购目录"
              clearable
            />
          </el-form-item>

          <el-form-item label="最高限制单价（元）" prop="maxPrice" required>
            <el-input-number
              v-model="target2EditForm.maxPrice"
              :min="0"
              :precision="2"
              placeholder="请输入最高限制单价"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="数量" prop="quantity" required>
            <el-input
              v-model="target2EditForm.quantity"
              placeholder="请输入数量（如：不限、1项等）"
              clearable
            />
          </el-form-item>

          <el-form-item label="所属行业" prop="industry" required>
            <el-select
              v-model="target2EditForm.industry"
              placeholder="请选择所属行业"
              clearable
              style="width: 100%"
            >
              <el-option label="工程" value="工程" />
              <el-option label="建筑" value="建筑" />
              <el-option label="装修" value="装修" />
              <el-option label="市政" value="市政" />
              <el-option label="园林" value="园林" />
              <el-option label="水利" value="水利" />
              <el-option label="交通" value="交通" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>

          <el-form-item label="总价（元）" prop="totalPrice" required>
            <el-input-number
              v-model="target2EditForm.totalPrice"
              :min="0"
              :precision="2"
              placeholder="请输入总价"
              style="width: 100%"
              @change="handleTarget2TotalPriceChange"
            />
            <div class="price-calculation-hint">
              <span class="hint-text">
                工程类项目总价通常根据工程量清单和市场价格确定
              </span>
            </div>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleTarget2DrawerClose">取消</el-button>
          <el-button type="primary" @click="saveTarget2EditForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 标的4编辑抽屉 -->
    <el-drawer
      v-model="target4EditDrawerVisible"
      title="编辑标的4基础信息"
      direction="rtl"
      size="650px"
      :before-close="handleTarget4DrawerClose"
    >
      <div class="target-edit-form">
        <el-form
          ref="target4EditFormRef"
          :model="target4EditForm"
          :rules="target4EditFormRules"
          label-width="140px"
          label-position="left"
        >
          <el-form-item label="标的名称" prop="name" required>
            <el-input
              v-model="target4EditForm.name"
              placeholder="请输入标的名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="采购目录" prop="catalog" required>
            <el-input
              v-model="target4EditForm.catalog"
              placeholder="请输入采购目录"
              clearable
            />
          </el-form-item>

          <el-form-item label="最高限制单价（元）" prop="maxPrice" required>
            <el-input-number
              v-model="target4EditForm.maxPrice"
              :min="0"
              :precision="2"
              placeholder="请输入最高限制单价"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="数量及单位" prop="quantity" required>
            <el-input
              v-model="target4EditForm.quantity"
              placeholder="请输入数量及单位（如：2台、5套等）"
              clearable
            />
          </el-form-item>

          <el-form-item label="所属行业" prop="industry" required>
            <el-select
              v-model="target4EditForm.industry"
              placeholder="请选择所属行业"
              clearable
              style="width: 100%"
            >
              <el-option label="网络安全" value="网络安全" />
              <el-option label="信息技术" value="信息技术" />
              <el-option label="安防监控" value="安防监控" />
              <el-option label="办公设备" value="办公设备" />
              <el-option label="显示设备" value="显示设备" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>

          <el-form-item label="总价（元）" prop="totalPrice" required>
            <el-input-number
              v-model="target4EditForm.totalPrice"
              :min="0"
              :precision="2"
              placeholder="请输入总价"
              style="width: 100%"
              @change="handleTarget4TotalPriceChange"
            />
            <div class="price-calculation-hint">
              <span class="hint-text">
                建议总价：{{ suggestedTarget4TotalPrice }}元（最高限制单价 × 数量）
              </span>
            </div>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleTarget4DrawerClose">取消</el-button>
          <el-button type="primary" @click="saveTarget4EditForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 标的3编辑抽屉 -->
    <el-drawer
      v-model="target3EditDrawerVisible"
      title="编辑标的3基础信息"
      direction="rtl"
      size="650px"
      :before-close="handleTarget3DrawerClose"
    >
      <div class="target-edit-form">
        <el-form
          ref="target3EditFormRef"
          :model="target3EditForm"
          :rules="target3EditFormRules"
          label-width="140px"
          label-position="left"
        >
          <el-form-item label="标的名称" prop="name" required>
            <el-input
              v-model="target3EditForm.name"
              placeholder="请输入标的名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="采购目录" prop="catalog" required>
            <el-input
              v-model="target3EditForm.catalog"
              placeholder="请输入采购目录"
              clearable
            />
          </el-form-item>

          <el-form-item label="最高限制单价（元）" prop="maxPrice" required>
            <el-input-number
              v-model="target3EditForm.maxPrice"
              :min="0"
              :precision="2"
              placeholder="请输入最高限制单价"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="服务期限" prop="servicePeriod" required>
            <el-input
              v-model="target3EditForm.servicePeriod"
              placeholder="请输入服务期限（如：1年、6个月等）"
              clearable
            />
          </el-form-item>

          <el-form-item label="所属行业" prop="industry" required>
            <el-select
              v-model="target3EditForm.industry"
              placeholder="请选择所属行业"
              clearable
              style="width: 100%"
            >
              <el-option label="物业服务" value="物业服务" />
              <el-option label="保洁服务" value="保洁服务" />
              <el-option label="安保服务" value="安保服务" />
              <el-option label="餐饮服务" value="餐饮服务" />
              <el-option label="维修服务" value="维修服务" />
              <el-option label="咨询服务" value="咨询服务" />
              <el-option label="培训服务" value="培训服务" />
              <el-option label="技术服务" value="技术服务" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>

          <el-form-item label="服务内容" prop="serviceContent" required>
            <el-input
              v-model="target3EditForm.serviceContent"
              type="textarea"
              :rows="3"
              placeholder="请描述服务内容和目标"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="物业地址" prop="propertyAddress" required>
            <el-input
              v-model="target3EditForm.propertyAddress"
              placeholder="请输入物业地址"
              clearable
            />
          </el-form-item>

          <el-form-item label="物业管理内容清单" required>
            <div class="property-management-setting">
              <div class="setting-info">
                <span v-if="totalManagementItems > 0" class="setting-status">
                  已配置 {{ totalManagementItems }} 项内容（服务内容：{{ serviceContentList.length }}项，提供内容：{{ procurementContentList.length }}项）
                </span>
                <span v-else class="setting-status-empty">
                  暂未配置管理内容
                </span>
              </div>
              <el-button type="primary" @click="openPropertyManagementDialog">
                设置
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="总价（元）" prop="totalPrice" required>
            <el-input-number
              v-model="target3EditForm.totalPrice"
              :min="0"
              :precision="2"
              placeholder="请输入总价"
              style="width: 100%"
              @change="handleTarget3TotalPriceChange"
            />
            <div class="price-calculation-hint">
              <span class="hint-text">
                服务类项目总价通常根据服务期限和服务内容确定
              </span>
            </div>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleTarget3DrawerClose">取消</el-button>
          <el-button type="primary" @click="saveTarget3EditForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 物业管理内容清单弹窗 -->
    <el-dialog
      v-model="propertyManagementDialogVisible"
      title="物业管理内容清单"
      width="900px"
      :before-close="closePropertyManagementDialog"
    >
      <div class="property-management-container">
        <el-tabs v-model="activeManagementTab" class="management-tabs">
          <!-- 物业管理服务内容清单 -->
          <el-tab-pane label="物业管理服务内容清单" name="service">
            <div class="tab-content">
              <div class="management-header">
                <div class="header-info">
                  <span class="info-text">配置物业管理服务的具体内容和标准</span>
                </div>
                <el-button type="primary" @click="addServiceItem">
                  <el-icon><Plus /></el-icon>
                  新增服务内容
                </el-button>
              </div>

        <div class="management-list">
          <el-table :data="propertyManagementList" border style="width: 100%">
            <el-table-column label="序号"  align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="一级指标" width="120">
              <template #default="scope">
                <el-input
                  v-model="scope.row.name"
                  placeholder="服务名称"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="二级指标" min-width="120">
              <template #default="scope">
                <el-input
                  v-model="scope.row.description"
                  placeholder="详细描述服务内容"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="指标项明细" min-width="200">
              <template #default="scope">
                <el-input
                  v-model="scope.row.standard"
                  placeholder="服务标准要求"
                  size="small"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="removePropertyManagementItem(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 采购人内容提供清单 -->
          <el-tab-pane label="采购人内容提供清单" name="procurement">
            <div class="tab-content">
              <div class="management-header">
                <div class="header-info">
                  <span class="info-text">采购人提供供应商使用的场地、设施、设备、材料等</span>
                </div>
                <el-button type="primary" @click="addProcurementItem">
                  <el-icon><Plus /></el-icon>
                  新增提供内容
                </el-button>
              </div>

              <div class="management-list">
                <el-table :data="procurementContentList" border style="width: 100%">
                  <el-table-column label="序号" align="center">
                    <template #default="scope">
                      {{ scope.$index + 1 }}
                    </template>
                  </el-table-column>

                  <el-table-column label="提供内容" width="150">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.name"
                        placeholder="具体提供内容"
                        size="small"
                      />
                    </template>
                  </el-table-column>

                  <el-table-column label="详细说明" min-width="200">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.description"
                        placeholder="详细说明提供内容的具体要求"
                        size="small"
                      />
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="80" align="center">
                    <template #default="scope">
                      <el-button
                        type="danger"
                        link
                        size="small"
                        @click="removeProcurementItem(scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePropertyManagementDialog">取消</el-button>
          <el-button type="primary" @click="savePropertyManagementList">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Document, Check, QuestionFilled, Plus, Delete, Upload, Download, Edit } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { TabsPaneContext } from 'element-plus'

// 主导航Tab状态
const mainActiveTab = ref('review-rules') // 默认显示评审规则（当前步骤）
const completedTabs = ref(['basic-info', 'process', 'requirements', 'qualification', 'bidding']) // 已完成的步骤

// 分包Tab状态
const activeTab = ref('package1')
const configActiveTab = ref('basic')

// 采购需求Tab状态
const requirementsActiveTab = ref('item1')

// 主导航Tab点击处理
const handleMainTabClick = (tab: TabsPaneContext) => {
  console.log('切换到Tab:', tab.paneName)
  // 这里可以添加切换逻辑，比如保存当前表单数据等
}

// 标的1编辑抽屉相关
const target1EditDrawerVisible = ref(false)
const target1FormRef = ref()

// 标的1编辑表单数据
const target1EditForm = ref({
  name: '',
  catalog: '',
  maxPrice: 0,
  quantity: 1,
  unit: '',
  industry: '',
  totalPrice: 0
})

// 表单验证规则
const target1FormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请选择采购目录', trigger: 'change' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}

// 打开标的1编辑抽屉
const openTarget1EditDrawer = () => {
  // 填充当前数据到表单
  const currentData = target1BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))
  const quantity = parseInt(currentData.quantity.replace('台', ''))

  target1EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    quantity: quantity,
    unit: '台', // 从quantity字段中提取
    industry: currentData.industry,
    totalPrice: maxPrice * quantity // 计算总价
  }
  target1EditDrawerVisible.value = true
}

// 关闭抽屉
const handleTarget1DrawerClose = () => {
  target1EditDrawerVisible.value = false
}

// 处理总价变化
const handleTotalPriceChange = (value: number) => {
  // 这里可以添加总价变化时的逻辑，比如验证或计算
  console.log('总价已更改为:', value)
}

// 计算建议总价
const suggestedTotalPrice = computed(() => {
  return (target1EditForm.value.maxPrice * target1EditForm.value.quantity).toFixed(2)
})

// 计算标的4建议总价
const suggestedTarget4TotalPrice = computed(() => {
  // 从数量字符串中提取数字
  const quantityMatch = target4EditForm.value.quantity.match(/\d+/)
  const quantity = quantityMatch ? parseInt(quantityMatch[0]) : 1
  return (target4EditForm.value.maxPrice * quantity).toFixed(2)
})

// 标的2编辑相关
const target2EditDrawerVisible = ref(false)

// 标的2编辑表单数据
const target2EditForm = ref({
  name: '',
  catalog: '',
  maxPrice: 0,
  quantity: '',
  industry: '',
  totalPrice: 0
})

// 标的2表单验证规则
const target2EditFormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' },
    { min: 2, message: '标的名称至少2个字符', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请输入采购目录', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '最高限制单价必须大于0', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}

// 标的4编辑相关
const target4EditDrawerVisible = ref(false)

// 标的4编辑表单数据
const target4EditForm = ref({
  name: '',
  catalog: '',
  maxPrice: 0,
  quantity: '',
  industry: '',
  totalPrice: 0
})

// 标的4表单验证规则
const target4EditFormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' },
    { min: 2, max: 50, message: '标的名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请输入采购目录', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '最高限制单价必须大于0', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量及单位', trigger: 'blur' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}

// 标的3编辑相关
const target3EditDrawerVisible = ref(false)

// 物业管理内容清单相关
const propertyManagementDialogVisible = ref(false)
const activeManagementTab = ref('service') // 默认显示服务内容清单

// 物业管理服务内容清单
const serviceContentList = ref([
  {
    id: 1,
    name: '保洁服务',
    description: '日常清洁',
    standard: '每日清洁、垃圾清理、环境维护，符合物业管理标准'
  },
  {
    id: 2,
    name: '安保服务',
    description: '安全管理',
    standard: '门禁管理、巡逻检查、监控维护，24小时值守'
  },
  {
    id: 3,
    name: '设施维修',
    description: '维修保养',
    standard: '水电维修、设备保养、应急维修，及时响应'
  }
])

// 采购人内容提供清单
const procurementContentList = ref([
  {
    id: 1,
    category: '场地提供',
    name: '办公场所',
    description: '提供物业管理人员办公场所，面积不少于20平方米',
    timing: '服务开始前',
    remark: '包含基本办公设施'
  },
  {
    id: 2,
    category: '设备提供',
    name: '清洁工具',
    description: '提供基础清洁工具和设备存放空间',
    timing: '服务开始前',
    remark: '定期更新维护'
  },
  {
    id: 3,
    category: '人员配合',
    name: '联络人员',
    description: '指定专门联络人员，负责日常沟通协调',
    timing: '服务期间',
    remark: '保持24小时联系畅通'
  }
])

// 保持原有的propertyManagementList用于兼容性
const propertyManagementList = ref(serviceContentList.value)

// 计算总的管理内容数量
const totalManagementItems = computed(() => {
  return serviceContentList.value.length + procurementContentList.value.length
})

// 标的3编辑表单数据
const target3EditForm = ref({
  name: '',
  catalog: '',
  maxPrice: 0,
  servicePeriod: '',
  industry: '',
  serviceContent: '',
  propertyAddress: '',
  totalPrice: 0
})

// 标的3表单验证规则
const target3EditFormRules = {
  name: [
    { required: true, message: '请输入标的名称', trigger: 'blur' },
    { min: 2, message: '标的名称至少2个字符', trigger: 'blur' }
  ],
  catalog: [
    { required: true, message: '请输入采购目录', trigger: 'blur' }
  ],
  maxPrice: [
    { required: true, message: '请输入最高限制单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '最高限制单价必须大于0', trigger: 'blur' }
  ],
  servicePeriod: [
    { required: true, message: '请输入服务期限', trigger: 'blur' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  serviceContent: [
    { required: true, message: '请输入服务内容', trigger: 'blur' },
    { min: 10, message: '服务内容至少10个字符', trigger: 'blur' }
  ],
  propertyAddress: [
    { required: true, message: '请输入物业地址', trigger: 'blur' },
    { min: 5, message: '物业地址至少5个字符', trigger: 'blur' }
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '总价必须大于0', trigger: 'blur' }
  ]
}

// 保存标的1信息
const saveTarget1Info = async () => {
  try {
    await target1FormRef.value.validate()

    // 更新标的1的数据
    target1BasicInfo.value[0] = {
      name: target1EditForm.value.name,
      catalog: target1EditForm.value.catalog,
      maxPrice: `${target1EditForm.value.maxPrice}元`,
      quantity: `${target1EditForm.value.quantity}${target1EditForm.value.unit}`,
      industry: target1EditForm.value.industry
    }

    // 关闭抽屉
    target1EditDrawerVisible.value = false

    // 提示保存成功
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 政府采购政策配置相关方法
const editTarget1Policy = () => {
  policyEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的1
  currentEditingTarget.value = 'target1'
}

const configureTarget1Policy = () => {
  ElMessage.info('配置政府采购政策')
  // 配置完成后更新状态
  target1Requirements.value.policy.configured = true
  target1Requirements.value.policy.count = 1
}

// 技术要求配置相关方法
const editTarget1Technical = () => {
  technicalEditDrawerVisible.value = true
}

const configureTarget1Technical = () => {
  ElMessage.info('配置技术要求')
  // 配置完成后更新状态
  target1Requirements.value.technical.configured = true
  target1Requirements.value.technical.count = 2
}

// 商务要求配置相关方法
const editTarget1Business = () => {
  businessEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的1
  currentEditingTarget.value = 'target1'
}

const configureTarget1Business = () => {
  ElMessage.info('配置商务要求')
  // 配置完成后更新状态
  target1Requirements.value.business.configured = true
  target1Requirements.value.business.count = 3
}

// 政策抽屉相关方法
const handlePolicyDrawerClose = () => {
  policyEditDrawerVisible.value = false
}

// 添加政策项
const addPolicyItem = () => {
  const newId = Math.max(...policyList.value.map(item => item.id)) + 1
  policyList.value.push({
    id: newId,
    policyType: '',
    requirements: [],
    isSubstantive: false
  })
}

// 删除政策项
const removePolicyItem = (index: number) => {
  if (policyList.value.length > 1) {
    policyList.value.splice(index, 1)
  }
}

// 保存政策配置
const savePolicyConfig = () => {
  // 验证所有表单项
  let hasError = false

  for (const item of policyList.value) {
    if (!item.policyType || item.requirements.length === 0) {
      hasError = true
      break
    }
  }

  if (hasError) {
    ElMessage.error('请完善所有政策配置信息')
    return
  }

  // 根据当前编辑的标的更新配置状态
  if (currentEditingTarget.value === 'target1') {
    target1Requirements.value.policy.count = policyList.value.length
    target1Requirements.value.policy.configured = true
  } else if (currentEditingTarget.value === 'target2') {
    target2Requirements.value.policy.count = policyList.value.length
    target2Requirements.value.policy.configured = true
  } else if (currentEditingTarget.value === 'target4') {
    target4Requirements.value.policy.count = policyList.value.length
    target4Requirements.value.policy.configured = true
  }

  // 关闭抽屉
  policyEditDrawerVisible.value = false

  // 提示保存成功
  const targetName = currentEditingTarget.value === 'target1' ? '标的1' : '标的2'
  ElMessage.success(`${targetName}政策配置保存成功`)
}

// 商务要求抽屉相关方法
const handleBusinessDrawerClose = () => {
  businessEditDrawerVisible.value = false
}

// 添加商务要求项
const addBusinessItem = () => {
  const newId = Math.max(...businessList.value.map(item => item.id)) + 1
  businessList.value.push({
    id: newId,
    type: '',
    content: '',
    isReviewFactor: false,
    isSubstantive: false
  })
}

// 删除商务要求项
const removeBusinessItem = (index: number) => {
  if (businessList.value.length > 1) {
    businessList.value.splice(index, 1)
  }
}

// 保存商务要求配置
const saveBusinessConfig = () => {
  // 验证所有表单项
  let hasError = false

  for (const item of businessList.value) {
    if (!item.type || !item.content || item.content.length < 10) {
      hasError = true
      break
    }
  }

  if (hasError) {
    ElMessage.error('请完善所有商务要求信息，要求内容至少10个字符')
    return
  }

  // 根据当前编辑的标的更新配置状态
  if (currentEditingTarget.value === 'target1') {
    target1Requirements.value.business.count = businessList.value.length
    target1Requirements.value.business.configured = true
  } else if (currentEditingTarget.value === 'target2') {
    target2Requirements.value.business.count = businessList.value.length
    target2Requirements.value.business.configured = true
  } else if (currentEditingTarget.value === 'target3') {
    target3Requirements.value.business.count = businessList.value.length
    target3Requirements.value.business.configured = true
  } else if (currentEditingTarget.value === 'target4') {
    target4Requirements.value.business.count = businessList.value.length
    target4Requirements.value.business.configured = true
  }

  // 关闭抽屉
  businessEditDrawerVisible.value = false

  // 提示保存成功
  let targetName = '标的1'
  if (currentEditingTarget.value === 'target2') {
    targetName = '标的2'
  } else if (currentEditingTarget.value === 'target3') {
    targetName = '标的3'
  } else if (currentEditingTarget.value === 'target4') {
    targetName = '标的4'
  }
  ElMessage.success(`${targetName}商务要求配置保存成功`)
}

// 技术要求抽屉相关方法
const handleTechnicalDrawerClose = () => {
  technicalEditDrawerVisible.value = false
}

// 添加技术要求项
const addTechnicalItem = () => {
  const newId = Math.max(...technicalList.value.map(item => item.id)) + 1
  technicalList.value.push({
    id: newId,
    category: '',
    indicatorMode: 'single',
    indicatorName: '',
    primaryIndicator: '',
    secondaryIndicator: '',
    requirement: '',
    isReviewFactor: false,
    isSubstantive: false
  })
}

// 删除技术要求项
const removeTechnicalItem = (index: number) => {
  if (technicalList.value.length > 1) {
    technicalList.value.splice(index, 1)
  }
}

// 保存技术要求配置
const saveTechnicalConfig = () => {
  // 验证所有表单项
  let hasError = false
  let errorMessage = ''

  for (const item of technicalList.value) {
    // 基础字段验证
    if (!item.category || !item.requirement || item.requirement.length < 10) {
      hasError = true
      errorMessage = '请完善所有技术要求信息，指标要求至少10个字符'
      break
    }

    // 指标名称验证（根据模式不同验证不同字段）
    if (item.indicatorMode === 'single') {
      if (!item.indicatorName) {
        hasError = true
        errorMessage = '请输入指标名称'
        break
      }
    } else if (item.indicatorMode === 'hierarchical') {
      if (!item.primaryIndicator || !item.secondaryIndicator) {
        hasError = true
        errorMessage = '请输入一级和二级指标名称'
        break
      }
    }
  }

  if (hasError) {
    ElMessage.error(errorMessage)
    return
  }

  // 根据当前编辑的标的更新配置状态
  if (currentEditingTarget.value === 'target1') {
    target1Requirements.value.technical.count = technicalList.value.length
    target1Requirements.value.technical.configured = true
  } else if (currentEditingTarget.value === 'target2') {
    target2Requirements.value.technical.count = technicalList.value.length
    target2Requirements.value.technical.configured = true
  } else if (currentEditingTarget.value === 'target4') {
    target4Requirements.value.technical.count = technicalList.value.length
    target4Requirements.value.technical.configured = true
  }

  // 关闭抽屉
  technicalEditDrawerVisible.value = false

  // 提示保存成功
  ElMessage.success('技术要求配置保存成功')
}

// 服务要求抽屉相关方法
const handleServiceDrawerClose = () => {
  serviceEditDrawerVisible.value = false
}

// 添加服务要求项
const addServiceRequirementItem = () => {
  const newId = Math.max(...serviceList.value.map(item => item.id)) + 1
  serviceList.value.push({
    id: newId,
    category: '',
    serviceName: '',
    requirement: '',
    isReviewFactor: false,
    isSubstantive: false
  })
}

// 删除服务要求项
const removeServiceRequirementItem = (index: number) => {
  if (serviceList.value.length > 1) {
    serviceList.value.splice(index, 1)
  }
}

// 保存服务要求配置
const saveServiceConfig = () => {
  // 验证所有表单项
  let hasError = false
  let errorMessage = ''

  for (const item of serviceList.value) {
    // 基础字段验证
    if (!item.category || !item.serviceName || !item.requirement || item.requirement.length < 10) {
      hasError = true
      errorMessage = '请完善所有服务要求信息，具体要求至少10个字符'
      break
    }
  }

  if (hasError) {
    ElMessage.error(errorMessage)
    return
  }

  // 更新配置状态
  target3Requirements.value.service.count = serviceList.value.length
  target3Requirements.value.service.configured = true

  // 关闭抽屉
  serviceEditDrawerVisible.value = false

  // 提示保存成功
  ElMessage.success('服务要求配置保存成功')
}

// 导入服务要求
const importServiceRequirements = () => {
  ElMessage.info('导入服务要求功能')
}

// 导出服务要求
const exportServiceRequirements = () => {
  ElMessage.info('导出服务要求功能')
}

// 引用服务需求标准
const referenceServiceStandards = () => {
  referenceStandardDialogVisible.value = true
  // 默认选择服务类标准
  selectedCategoryId.value = 'service'
}

// 服务要求对比国标要求
const openServiceStandardComparisonDialog = (serviceItem: any, index: number) => {
  currentTechnicalItem.value = { ...serviceItem }
  currentTechnicalIndex.value = index
  standardComparisonDialogVisible.value = true
}

// 服务要求属性值参考
const openServiceAttributeReferenceDialog = (serviceItem: any, index: number) => {
  currentTechnicalItem.value = { ...serviceItem }
  currentTechnicalIndex.value = index
  selectedAttributeValue.value = ''
  attributeReferenceDialogVisible.value = true
}

// 技术要求操作方法
// 导入技术要求
const importTechnicalRequirements = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json,.xlsx,.xls'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          if (file.name.endsWith('.json')) {
            // 处理JSON文件
            const data = JSON.parse(e.target?.result as string)
            if (Array.isArray(data)) {
              // 验证数据格式
              const validData = data.filter(item =>
                item.category && item.requirement &&
                (item.indicatorName || (item.primaryIndicator && item.secondaryIndicator))
              )

              if (validData.length > 0) {
                // 为导入的数据分配新的ID
                const maxId = Math.max(...technicalList.value.map(item => item.id))
                validData.forEach((item, index) => {
                  item.id = maxId + index + 1
                  // 确保必要字段存在
                  if (!item.indicatorMode) item.indicatorMode = 'single'
                  if (!item.hasOwnProperty('isReviewFactor')) item.isReviewFactor = false
                  if (!item.hasOwnProperty('isSubstantive')) item.isSubstantive = false
                })

                technicalList.value.push(...validData)
                ElMessage.success(`成功导入${validData.length}条技术要求`)
              } else {
                ElMessage.error('文件格式不正确或没有有效数据')
              }
            } else {
              ElMessage.error('文件格式不正确，应为数组格式')
            }
          } else {
            ElMessage.info('Excel文件导入功能开发中，请使用JSON格式文件')
          }
        } catch (error) {
          ElMessage.error('文件解析失败，请检查文件格式')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 导出技术要求
const exportTechnicalRequirements = () => {
  if (technicalList.value.length === 0) {
    ElMessage.warning('暂无技术要求数据可导出')
    return
  }

  // 准备导出数据
  const exportData = technicalList.value.map(item => ({
    分类: item.category,
    指标模式: item.indicatorMode === 'single' ? '单级指标' : '二级指标',
    指标名称: item.indicatorMode === 'single' ? item.indicatorName : `${item.primaryIndicator}/${item.secondaryIndicator}`,
    指标要求: item.requirement,
    评审因素: item.isReviewFactor ? '是' : '否',
    实质性响应要求: item.isSubstantive ? '是' : '否'
  }))

  // 创建JSON文件并下载
  const dataStr = JSON.stringify(technicalList.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `技术要求_${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  URL.revokeObjectURL(url)

  ElMessage.success('技术要求导出成功')
}

// 引用需求标准
const referenceStandards = () => {
  referenceStandardDialogVisible.value = true
}

// 分类编辑相关方法
// 打开分类编辑对话框
const editCategories = () => {
  categoryEditDialogVisible.value = true
}

// 关闭分类编辑对话框
const handleCategoryDialogClose = () => {
  categoryEditDialogVisible.value = false
}

// 添加分类
const addCategory = () => {
  categoryList.value.push({
    name: '',
    description: ''
  })
}

// 删除分类
const removeCategory = (index: number) => {
  if (categoryList.value.length > 1) {
    categoryList.value.splice(index, 1)
  }
}

// 添加预设分类
const addPresetCategory = (preset: any) => {
  // 检查是否已存在相同名称的分类
  const exists = categoryList.value.some(category => category.name === preset.label)
  if (!exists) {
    categoryList.value.push({
      name: preset.label,
      description: preset.description
    })
    ElMessage.success(`已添加分类：${preset.label}`)
  } else {
    ElMessage.warning(`分类"${preset.label}"已存在`)
  }
}

// 恢复默认分类
const resetToPresetCategories = () => {
  ElMessageBox.confirm(
    '确定要恢复默认分类吗？这将清除所有自定义分类。',
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    categoryList.value = presetCategories.slice(0, 8).map(preset => ({
      name: preset.label,
      description: preset.description
    }))
    ElMessage.success('已恢复默认分类')
  }).catch(() => {
    // 用户取消操作
  })
}

// 保存分类配置
const saveCategoryConfig = () => {
  // 验证分类名称不能为空
  const emptyCategories = categoryList.value.filter(category => !category.name.trim())
  if (emptyCategories.length > 0) {
    ElMessage.error('请填写所有分类的名称')
    return
  }

  // 检查重复的分类名称
  const categoryNames = categoryList.value.map(category => category.name.trim())
  const uniqueNames = [...new Set(categoryNames)]
  if (categoryNames.length !== uniqueNames.length) {
    ElMessage.error('分类名称不能重复')
    return
  }

  // 更新技术要求表格中的分类选项
  updateCategoryOptions()

  // 关闭对话框
  categoryEditDialogVisible.value = false

  ElMessage.success('分类配置保存成功')
}

// 更新分类选项（用于技术要求表格中的下拉选择）
const updateCategoryOptions = () => {
  // 这里可以更新技术要求表格中分类下拉选择的选项
  // 实际应用中可能需要更新全局的分类选项状态
  console.log('分类选项已更新:', categoryList.value)
}

// 表单数据
const maxPrice = ref('300,000.00')
const contractPeriod = ref('12')
const procurementDesc = ref('满足')
const remark = ref('')
const smePolicy = ref('是否')
const startDate = ref('2025-07-23')
const endDate = ref('2025-07-30 20:57')
const morningTime = ref('00:00:00')
const morningEndTime = ref('12:00:00')
const afternoonTime = ref('12:00:00')
const afternoonEndTime = ref('23:59:59')
const fileMethod = ref('供应商登录政务云平台在线获取采购文件。进入项目采购应用，在获取采购文件菜单中进行操作，申请获取采购文件。')
const filePrice = ref('不收费')

// 采购需求数据
const target1BasicInfo = ref([
  {
    name: '台式计算机',
    catalog: 'A01010101台式计算机',
    maxPrice: '100000000.00元',
    quantity: '10（单位：台）',
    industry: '制造业',
  }
])

const target2BasicInfo = ref([
  {
    name: '办公用房工程',
    catalog: 'B01010000	办公用房施工',
    maxPrice: '800000元',
    quantity: '不限',
    industry: '工程',
    function: 'xxxxx',
    standard: 'xxxx'
  }
])

const target3BasicInfo = ref([
  {
    name: '物业管理服务',
    catalog: 'C1204物业管理服务',
    maxPrice: '50000元',
    quantity: '1年',
    industry: '物业服务',
    function: '提供办公楼宇物业管理服务',
    standard: '物业服务国家标准'
  }
])

const target4BasicInfo = ref([
  {
    name: '安全设备',
    catalog: '采购目录',
    maxPrice: '12000元',
    quantity: '2台',
    industry: '网络安全'
  }
])

const target5BasicInfo = ref([
  {
    name: '监控设备',
    catalog: '采购目录',
    maxPrice: '6000元',
    quantity: '8台',
    industry: '安防监控',
    function: 'xxxxx',
    standard: 'xxxx'
  }
])

const target6BasicInfo = ref([
  {
    name: '打印设备',
    catalog: '采购目录',
    maxPrice: '3000元',
    quantity: '6台',
    industry: '办公设备',
    function: 'xxxxx',
    standard: 'xxxx'
  }
])

const target7BasicInfo = ref([
  {
    name: '投影设备',
    catalog: '采购目录',
    maxPrice: '5000元',
    quantity: '4台',
    industry: '显示设备',
    function: 'xxxxx',
    standard: 'xxxx'
  }
])

// 标的1要求配置状态
const target1Requirements = ref({
  policy: {
    configured: true,
    count: 3
  },
  technical: {
    configured: false,
    count: 0
  },
  business: {
    configured: true,
    count: 5
  }
})

// 标的2要求配置状态
const target2Requirements = ref({
  policy: {
    configured: false,
    count: 0
  },
  technical: {
    configured: false,
    count: 0
  },
  business: {
    configured: false,
    count: 0
  }
})

// 标的4要求配置状态
const target4Requirements = ref({
  policy: {
    configured: false,
    count: 0
  },
  technical: {
    configured: false,
    count: 0
  },
  business: {
    configured: false,
    count: 0
  }
})

// 标的3要求配置状态
const target3Requirements = ref({
  service: {
    configured: false,
    count: 0
  },
  business: {
    configured: false,
    count: 0
  }
})

// 政府采购政策编辑抽屉相关
const policyEditDrawerVisible = ref(false)

// 当前编辑的标的（用于区分标的1和标的2）
const currentEditingTarget = ref('target1')

// 政策配置列表
const policyList = ref([
  {
    id: 1,
    policyType: 'sme_development',
    requirements: ['mandatory'],
    isSubstantive: true
  },
  {
    id: 2,
    policyType: 'energy_saving',
    requirements: ['priority'],
    isSubstantive: false
  },
  {
    id: 3,
    policyType: 'innovation_product',
    requirements: ['mandatory', 'priority'],
    isSubstantive: true
  }
])

// 政策表单验证规则
const policyFormRules = {
  policyType: [
    { required: true, message: '请选择政府采购政策', trigger: 'change' }
  ],
  requirements: [
    { required: true, message: '请选择落实要求', trigger: 'change' }
  ],
  isSubstantive: [
    { required: true, message: '请选择是否作为实质性响应要求', trigger: 'change' }
  ]
}

// 商务要求编辑抽屉相关
const businessEditDrawerVisible = ref(false)

// 商务要求配置列表
const businessList = ref([
  {
    id: 1,
    type: 'qualification',
    content: '供应商应具备相关行业资质证书，包括但不限于营业执照、税务登记证等',
    isReviewFactor: true,
    isSubstantive: true
  },
  {
    id: 2,
    type: 'performance',
    content: '供应商近三年内应具有类似项目实施经验，提供相关业绩证明材料',
    isReviewFactor: true,
    isSubstantive: false
  },
  {
    id: 3,
    type: 'financial',
    content: '供应商应具备良好的财务状况，提供近两年审计报告',
    isReviewFactor: false,
    isSubstantive: true
  },
  {
    id: 4,
    type: 'service',
    content: '供应商应提供7×24小时技术支持服务，响应时间不超过4小时',
    isReviewFactor: true,
    isSubstantive: false
  },
  {
    id: 5,
    type: 'delivery',
    content: '货物交付时间不得超过合同签订后30个工作日',
    isReviewFactor: false,
    isSubstantive: true
  }
])

// 商务要求表单验证规则
const businessFormRules = {
  type: [
    { required: true, message: '请选择商务要求类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入要求内容', trigger: 'blur' },
    { min: 10, message: '要求内容至少10个字符', trigger: 'blur' }
  ],
  isReviewFactor: [
    { required: true, message: '请选择是否作为评审因素', trigger: 'change' }
  ],
  isSubstantive: [
    { required: true, message: '请选择是否作为实质性响应要求', trigger: 'change' }
  ]
}

// 技术要求编辑抽屉相关
const technicalEditDrawerVisible = ref(false)

// 服务要求编辑抽屉相关
const serviceEditDrawerVisible = ref(false)

// 分类编辑对话框相关
const categoryEditDialogVisible = ref(false)

// 分类列表
const categoryList = ref([
  { name: '硬件规格', description: '硬件设备的技术规格要求' },
  { name: '软件要求', description: '软件系统的功能和性能要求' },
  { name: '性能指标', description: '系统性能相关的技术指标' },
  { name: '接口标准', description: '接口协议和标准规范' },
  { name: '安全要求', description: '信息安全和数据保护要求' },
  { name: '兼容性', description: '系统兼容性和互操作性要求' },
  { name: '环境要求', description: '运行环境和部署环境要求' },
  { name: '标准规范', description: '相关行业标准和规范要求' }
])

// 预设分类
const presetCategories = [
  { label: '硬件规格', value: 'hardware', description: '硬件设备的技术规格要求' },
  { label: '软件要求', value: 'software', description: '软件系统的功能和性能要求' },
  { label: '性能指标', value: 'performance', description: '系统性能相关的技术指标' },
  { label: '接口标准', value: 'interface', description: '接口协议和标准规范' },
  { label: '安全要求', value: 'security', description: '信息安全和数据保护要求' },
  { label: '兼容性', value: 'compatibility', description: '系统兼容性和互操作性要求' },
  { label: '环境要求', value: 'environment', description: '运行环境和部署环境要求' },
  { label: '标准规范', value: 'standard', description: '相关行业标准和规范要求' },
  { label: '质量要求', value: 'quality', description: '产品质量和服务质量要求' },
  { label: '维护要求', value: 'maintenance', description: '系统维护和技术支持要求' }
]

// 技术要求配置列表
const technicalList = ref([
  {
    id: 1,
    category: 'hardware',
    indicatorMode: 'single',
    indicatorName: 'CPU性能',
    primaryIndicator: '',
    secondaryIndicator: '',
    requirement: '处理器应不低于Intel Core i7-10700或AMD Ryzen 7 3700X，主频不低于3.6GHz，支持超线程技术',
    isReviewFactor: true,
    isSubstantive: true
  },
  {
    id: 2,
    category: 'hardware',
    indicatorMode: 'hierarchical',
    indicatorName: '',
    primaryIndicator: '内存规格',
    secondaryIndicator: '容量要求',
    requirement: '系统内存不少于16GB DDR4-3200，支持双通道，可扩展至64GB',
    isReviewFactor: true,
    isSubstantive: false
  },
  {
    id: 3,
    category: 'performance',
    indicatorMode: 'hierarchical',
    indicatorName: '',
    primaryIndicator: '网络性能',
    secondaryIndicator: '传输指标',
    requirement: '网络接口应支持千兆以太网，网络延迟不超过1ms，吞吐量不低于900Mbps',
    isReviewFactor: false,
    isSubstantive: true
  }
])

// 服务要求配置列表
const serviceList = ref([
  {
    id: 1,
    category: 'quality',
    serviceName: '保洁服务质量',
    requirement: '每日清洁办公区域，垃圾清理及时，保持环境整洁，符合物业管理服务标准',
    isReviewFactor: true,
    isSubstantive: true
  },
  {
    id: 2,
    category: 'personnel',
    serviceName: '服务人员要求',
    requirement: '配备专业物业管理人员，持有相关资格证书，具备良好的服务意识和沟通能力',
    isReviewFactor: true,
    isSubstantive: false
  },
  {
    id: 3,
    category: 'time',
    serviceName: '服务时间安排',
    requirement: '提供7×24小时值守服务，工作日8:00-18:00为正常服务时间，其他时间为应急服务',
    isReviewFactor: false,
    isSubstantive: true
  },
  {
    id: 4,
    category: 'emergency',
    serviceName: '应急响应时间',
    requirement: '接到报修或投诉后，一般问题2小时内响应，紧急问题30分钟内到达现场',
    isReviewFactor: true,
    isSubstantive: true
  }
])

// 技术要求表单验证规则
const technicalFormRules = {
  category: [
    { required: true, message: '请选择或输入分类', trigger: 'change' }
  ],
  indicatorName: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  primaryIndicator: [
    { required: true, message: '请输入一级指标名称', trigger: 'blur' }
  ],
  secondaryIndicator: [
    { required: true, message: '请输入二级指标名称', trigger: 'blur' }
  ],
  requirement: [
    { required: true, message: '请输入指标要求', trigger: 'blur' },
    { min: 10, message: '指标要求至少10个字符', trigger: 'blur' }
  ],
  isReviewFactor: [
    { required: true, message: '请选择是否作为评审因素', trigger: 'change' }
  ],
  isSubstantive: [
    { required: true, message: '请选择是否作为实质性响应要求', trigger: 'change' }
  ]
}

// 对表国标要求弹窗相关
const standardComparisonDialogVisible = ref(false)
const currentTechnicalItem = ref<any>(null)
const currentTechnicalIndex = ref(-1)

// 国标对比数据
const standardComparisonList = ref([
  {
    id: 1,
    standardCode: 'GB/T 28181-2016',
    standardName: '公共安全视频监控联网系统信息传输、交换、控制技术要求',
    standardRequirement: '处理器性能应不低于Intel Core i5-8400或同等性能处理器，主频不低于2.8GHz',
    differenceLevel: 'minor',
    differenceDescription: '当前配置要求更高，使用i7处理器',
    suggestion: '当前配置符合并超出国标要求，建议保持'
  },
  {
    id: 2,
    standardCode: 'GB/T 20269-2006',
    standardName: '信息安全技术 信息系统安全管理要求',
    standardRequirement: '系统应具备基本的安全防护能力，处理器性能满足基础运算需求',
    differenceLevel: 'none',
    differenceDescription: '配置完全符合国标要求',
    suggestion: '配置合规，无需调整'
  }
])

// 服务类国标对比数据
const serviceStandardComparisonList = ref([
  {
    id: 1,
    standardCode: 'GB/T 20647.9-2006',
    standardName: '社区服务指南 第9部分：物业服务',
    standardRequirement: '保洁服务应每日清洁不少于1次，垃圾清理应及时',
    differenceLevel: 'none',
    differenceDescription: '当前配置符合国标要求，清洁频次为每日2次',
    suggestion: '配置符合并超出国标要求，建议保持'
  },
  {
    id: 2,
    standardCode: 'GB/T 50326-2017',
    standardName: '建设工程项目管理规范',
    standardRequirement: '服务人员应具备相应的专业资质和技能',
    differenceLevel: 'none',
    differenceDescription: '要求持有物业管理师证书，符合国标要求',
    suggestion: '配置合规，无需调整'
  },
  {
    id: 3,
    standardCode: 'GB/T 50328-2014',
    standardName: '建设工程文件归档规范',
    standardRequirement: '应急响应时间一般问题不超过4小时',
    differenceLevel: 'minor',
    differenceDescription: '当前要求2小时响应，标准更高',
    suggestion: '当前配置优于国标要求，建议保持'
  },
  {
    id: 4,
    standardCode: 'GB/T 19001-2016',
    standardName: '质量管理体系 要求',
    standardRequirement: '应建立质量管理体系，确保服务质量',
    differenceLevel: 'none',
    differenceDescription: '要求ISO9001认证，完全符合国标',
    suggestion: '配置合规，无需调整'
  }
])

// 属性值参考弹窗相关
const attributeReferenceDialogVisible = ref(false)
const attributeReferenceActiveTab = ref('common')
const selectedAttributeValue = ref('')

// 常用属性值数据
const commonAttributeValues = ref([
  {
    name: 'CPU性能等级',
    values: [
      { id: 1, text: 'Intel Core i3-10100或同等性能' },
      { id: 2, text: 'Intel Core i5-10400或同等性能' },
      { id: 3, text: 'Intel Core i7-10700或同等性能' },
      { id: 4, text: 'Intel Core i9-10900或同等性能' },
      { id: 5, text: 'AMD Ryzen 5 3600或同等性能' },
      { id: 6, text: 'AMD Ryzen 7 3700X或同等性能' }
    ]
  },
  {
    name: '内存规格',
    values: [
      { id: 7, text: '8GB DDR4-2666' },
      { id: 8, text: '16GB DDR4-3200' },
      { id: 9, text: '32GB DDR4-3200' },
      { id: 10, text: '64GB DDR4-3200' }
    ]
  },
  {
    name: '网络性能',
    values: [
      { id: 11, text: '千兆以太网接口' },
      { id: 12, text: '万兆以太网接口' },
      { id: 13, text: '网络延迟不超过1ms' },
      { id: 14, text: '吞吐量不低于900Mbps' }
    ]
  },
  {
    name: '服务时间标准',
    values: [
      { id: 15, text: '7×24小时值守服务' },
      { id: 16, text: '工作日8:00-18:00服务' },
      { id: 17, text: '工作日8:00-20:00服务' },
      { id: 18, text: '全年无休服务' }
    ]
  },
  {
    name: '响应时间要求',
    values: [
      { id: 19, text: '一般问题2小时内响应' },
      { id: 20, text: '一般问题4小时内响应' },
      { id: 21, text: '紧急问题30分钟内响应' },
      { id: 22, text: '紧急问题1小时内响应' }
    ]
  },
  {
    name: '清洁频次标准',
    values: [
      { id: 23, text: '每日清洁1次' },
      { id: 24, text: '每日清洁2次' },
      { id: 25, text: '每日清洁3次' },
      { id: 26, text: '按需清洁' }
    ]
  },
  {
    name: '人员资质要求',
    values: [
      { id: 27, text: '持有物业管理师证书' },
      { id: 28, text: '持有相关专业资格证书' },
      { id: 29, text: '具备3年以上工作经验' },
      { id: 30, text: '具备5年以上工作经验' }
    ]
  }
])

// 行业标准值数据
const industryStandardValues = ref([
  {
    industry: '政府机关',
    standardValue: 'Intel Core i5或同等性能，主频不低于2.4GHz',
    description: '满足日常办公需求'
  },
  {
    industry: '金融行业',
    standardValue: 'Intel Core i7或同等性能，主频不低于3.0GHz',
    description: '满足高并发交易处理'
  },
  {
    industry: '教育行业',
    standardValue: 'Intel Core i5或同等性能，主频不低于2.6GHz',
    description: '满足教学和管理需求'
  },
  {
    industry: '医疗行业',
    standardValue: 'Intel Core i7或同等性能，主频不低于3.2GHz',
    description: '满足医疗信息系统要求'
  },
  {
    industry: '物业服务',
    standardValue: '7×24小时值守，一般问题2小时内响应',
    description: '满足物业管理服务要求'
  },
  {
    industry: '保洁服务',
    standardValue: '每日清洁不少于2次，垃圾及时清理',
    description: '满足办公环境清洁要求'
  },
  {
    industry: '安保服务',
    standardValue: '24小时值守，紧急情况30分钟内响应',
    description: '满足安全防护要求'
  },
  {
    industry: '餐饮服务',
    standardValue: '工作日提供三餐，食品安全符合国家标准',
    description: '满足员工用餐需求'
  }
])

// 历史配置值数据
const historyAttributeValues = ref([
  {
    projectName: '某市政府采购项目',
    configValue: 'Intel Core i7-9700，主频3.0GHz，支持超线程',
    configTime: '2023-12-15'
  },
  {
    projectName: '教育局设备采购',
    configValue: 'Intel Core i5-10400，主频2.9GHz',
    configTime: '2024-01-20'
  },
  {
    projectName: '医院信息化建设',
    configValue: 'Intel Core i7-10700K，主频3.8GHz，支持超线程技术',
    configTime: '2024-02-10'
  },
  {
    projectName: '某大厦物业管理服务',
    configValue: '7×24小时值守服务，一般问题2小时内响应，紧急问题30分钟内到达',
    configTime: '2024-01-15'
  },
  {
    projectName: '政府办公楼保洁服务',
    configValue: '每日清洁2次，垃圾及时清理，保持环境整洁卫生',
    configTime: '2024-02-20'
  },
  {
    projectName: '学校餐饮服务采购',
    configValue: '工作日提供三餐，食品安全符合国家标准，营养搭配合理',
    configTime: '2024-03-10'
  }
])

// 引用需求标准弹窗相关
const referenceStandardDialogVisible = ref(false)
const selectedCategoryId = ref('database')
const selectedStandards = ref<any[]>([])

// 搜索表单
const standardSearchForm = ref({
  standardName: '',
  procurementCategory: '',
  indicatorName: ''
})

// 采购目录数据
const procurementCategories = ref([
  {
    id: 'database',
    name: '数据库政府采购需求标准',
    year: '(2023年版)',
    code: 'A010101C1数据库',
    count: 15
  },
  {
    id: 'os',
    name: '操作系统政府采购需求标准',
    year: '(2023年版)',
    code: 'A010101C2操作系统',
    count: 12
  },
  {
    id: 'network',
    name: '网络系统政府采购需求标准',
    year: '(2023年版)',
    code: 'A010101C3网络系统',
    count: 18
  },
  {
    id: 'security',
    name: '安全系统政府采购需求标准',
    year: '(2023年版)',
    code: 'A010101C4安全系统',
    count: 10
  },
  {
    id: 'service',
    name: '服务类政府采购需求标准',
    year: '(2023年版)',
    code: 'C010101服务类',
    count: 6
  }
])

// 需求标准数据
const standardsData = ref({
  database: [
    {
      id: 'db_001',
      indicatorCategory: '功能要求',
      primaryIndicator: '操作系统支持多cpu架构',
      secondaryIndicator: '同源兼容多cpu平台架构',
      requirement: '操作系统应支持ARM、LoongArch、MIPS、SW64、x86等平台架构CPU',
      usageDescription: '采用人工智能监控需要提供具体需求条件',
      otherAttributes: ['必须符合安全性要求', '必须满足人员需求', '可以作为评分因素']
    },
    {
      id: 'db_002',
      indicatorCategory: '系统要求',
      primaryIndicator: '操作系统支持多cpu架构',
      secondaryIndicator: '同源兼容多cpu平台架构',
      requirement: '操作系统应支持ARM、LoongArch、MIPS、SW64、x86等平台架构CPU',
      usageDescription: '采用人工智能监控需要提供具体需求条件',
      otherAttributes: ['必须符合安全性要求', '必须满足人员需求', '可以作为评分因素']
    },
    {
      id: 'db_003',
      indicatorCategory: '系统要求',
      primaryIndicator: '操作系统支持多cpu架构',
      secondaryIndicator: '同源兼容多cpu平台架构',
      requirement: '操作系统应支持ARM、LoongArch、MIPS、SW64、x86等平台架构CPU',
      usageDescription: '采用人工智能监控需要提供具体需求条件',
      otherAttributes: ['必须符合安全性要求', '必须满足人员需求', '可以作为评分因素']
    },
    {
      id: 'db_004',
      indicatorCategory: '性能要求',
      primaryIndicator: '数据库性能',
      secondaryIndicator: '并发处理能力',
      requirement: '支持不少于1000个并发连接，TPS不低于10000',
      usageDescription: '确保数据库在高并发场景下的稳定性',
      otherAttributes: ['性能基准要求', '可扩展性要求']
    },
    {
      id: 'db_005',
      indicatorCategory: '安全要求',
      primaryIndicator: '数据加密',
      secondaryIndicator: '传输加密',
      requirement: '支持TLS 1.2及以上版本，数据传输全程加密',
      usageDescription: '保障数据传输安全',
      otherAttributes: ['必须符合安全性要求', '等保合规要求']
    }
  ],
  os: [
    {
      id: 'os_001',
      indicatorCategory: '系统架构',
      primaryIndicator: '系统架构',
      secondaryIndicator: 'CPU支持',
      requirement: '支持x86_64、ARM64、MIPS64等主流架构',
      usageDescription: '确保系统兼容性和性能要求',
      otherAttributes: ['必须符合安全性要求', '性能基准要求']
    },
    {
      id: 'os_002',
      indicatorCategory: '安全特性',
      primaryIndicator: '安全特性',
      secondaryIndicator: '访问控制',
      requirement: '支持强制访问控制(MAC)和自主访问控制(DAC)',
      usageDescription: '满足等级保护要求',
      otherAttributes: ['必须符合安全性要求', '等保合规要求']
    },
    {
      id: 'os_003',
      indicatorCategory: '性能要求',
      primaryIndicator: '系统性能',
      secondaryIndicator: '启动时间',
      requirement: '系统启动时间不超过30秒，应用启动时间不超过10秒',
      usageDescription: '提升用户体验和工作效率',
      otherAttributes: ['性能基准要求', '用户体验要求']
    }
  ],
  network: [
    {
      id: 'net_001',
      indicatorCategory: '性能要求',
      primaryIndicator: '网络性能',
      secondaryIndicator: '带宽要求',
      requirement: '支持千兆以太网，背板带宽不低于480Gbps',
      usageDescription: '满足高并发网络处理需求',
      otherAttributes: ['性能基准要求', '可扩展性要求']
    },
    {
      id: 'net_002',
      indicatorCategory: '功能要求',
      primaryIndicator: '网络协议',
      secondaryIndicator: '协议支持',
      requirement: '支持IPv4/IPv6双栈，支持VLAN、QoS等网络特性',
      usageDescription: '确保网络功能完整性',
      otherAttributes: ['功能完整性要求', '标准兼容性要求']
    }
  ],
  security: [
    {
      id: 'sec_001',
      indicatorCategory: '安全要求',
      primaryIndicator: '加密算法',
      secondaryIndicator: '国密支持',
      requirement: '支持SM2、SM3、SM4等国产密码算法',
      usageDescription: '符合国家密码管理要求',
      otherAttributes: ['必须符合安全性要求', '国密合规要求']
    },
    {
      id: 'sec_002',
      indicatorCategory: '安全要求',
      primaryIndicator: '访问控制',
      secondaryIndicator: '身份认证',
      requirement: '支持多因子认证，包括密码、证书、生物特征等',
      usageDescription: '提高系统安全防护等级',
      otherAttributes: ['必须符合安全性要求', '多重验证要求']
    }
  ],
  service: [
    {
      id: 'srv_001',
      indicatorCategory: '服务质量',
      primaryIndicator: '保洁服务标准',
      secondaryIndicator: '清洁频次要求',
      requirement: '办公区域每日清洁不少于2次，垃圾清理及时，保持环境整洁卫生',
      usageDescription: '确保办公环境清洁卫生，符合物业管理服务标准',
      otherAttributes: ['必须符合卫生标准', '可以作为评分因素']
    },
    {
      id: 'srv_002',
      indicatorCategory: '服务人员',
      primaryIndicator: '人员资质要求',
      secondaryIndicator: '专业证书',
      requirement: '物业管理人员应持有物业管理师证书或相关专业资格证书',
      usageDescription: '确保服务人员具备专业能力',
      otherAttributes: ['必须符合资质要求', '实质性响应要求']
    },
    {
      id: 'srv_003',
      indicatorCategory: '服务时间',
      primaryIndicator: '值守时间安排',
      secondaryIndicator: '24小时服务',
      requirement: '提供7×24小时值守服务，工作日8:00-18:00为正常服务时间',
      usageDescription: '确保服务时间覆盖全天候需求',
      otherAttributes: ['服务时间要求', '应急响应要求']
    },
    {
      id: 'srv_004',
      indicatorCategory: '应急响应',
      primaryIndicator: '响应时间标准',
      secondaryIndicator: '紧急事件处理',
      requirement: '一般问题2小时内响应，紧急问题30分钟内到达现场',
      usageDescription: '确保及时响应和处理各类问题',
      otherAttributes: ['必须符合响应时间要求', '可以作为评分因素']
    },
    {
      id: 'srv_005',
      indicatorCategory: '服务标准',
      primaryIndicator: '服务质量管理',
      secondaryIndicator: 'ISO质量体系',
      requirement: '具备ISO9001质量管理体系认证，建立完善的服务质量控制体系',
      usageDescription: '确保服务质量的标准化和规范化',
      otherAttributes: ['必须符合质量标准', '体系认证要求']
    },
    {
      id: 'srv_006',
      indicatorCategory: '设备要求',
      primaryIndicator: '清洁设备配置',
      secondaryIndicator: '专业设备',
      requirement: '配备专业清洁设备，包括吸尘器、洗地机、高压清洗机等',
      usageDescription: '确保清洁作业的专业性和效率',
      otherAttributes: ['设备配置要求', '维护保养要求']
    }
  ]
})

// 当前显示的标准列表
const currentStandardList = computed(() => {
  return standardsData.value[selectedCategoryId.value as keyof typeof standardsData.value] || []
})

// 当前显示的国标对比列表
const currentStandardComparisonList = computed(() => {
  if (currentEditingTarget.value === 'target3') {
    return serviceStandardComparisonList.value
  }
  return standardComparisonList.value
})

// 对表国标要求相关方法
const openStandardComparisonDialog = (technicalItem: any, index: number) => {
  currentTechnicalItem.value = { ...technicalItem }
  currentTechnicalIndex.value = index
  standardComparisonDialogVisible.value = true
}

const handleStandardComparisonDialogClose = () => {
  standardComparisonDialogVisible.value = false
  currentTechnicalItem.value = null
  currentTechnicalIndex.value = -1
}

const applyStandardRequirement = () => {
  // 这里可以实现应用国标要求的逻辑
  // 例如：将选中的国标要求应用到当前技术要求中
  ElMessage.success('国标要求应用成功')
  handleStandardComparisonDialogClose()
}

// 属性值参考相关方法
const openAttributeReferenceDialog = (technicalItem: any, index: number) => {
  currentTechnicalItem.value = { ...technicalItem }
  currentTechnicalIndex.value = index
  selectedAttributeValue.value = ''
  attributeReferenceDialogVisible.value = true
}

const handleAttributeReferenceDialogClose = () => {
  attributeReferenceDialogVisible.value = false
  currentTechnicalItem.value = null
  currentTechnicalIndex.value = -1
  selectedAttributeValue.value = ''
}

const selectAttributeValue = (value: string) => {
  selectedAttributeValue.value = value
  ElMessage.success(`已选择属性值：${value}`)
}

const applySelectedAttributeValue = () => {
  if (!selectedAttributeValue.value) {
    ElMessage.warning('请先选择一个属性值')
    return
  }

  if (currentTechnicalIndex.value >= 0) {
    if (currentEditingTarget.value === 'target3') {
      // 将选择的属性值应用到当前服务要求中
      serviceList.value[currentTechnicalIndex.value].requirement = selectedAttributeValue.value
      ElMessage.success('属性值应用成功')
    } else {
      // 将选择的属性值应用到当前技术要求中
      technicalList.value[currentTechnicalIndex.value].requirement = selectedAttributeValue.value
      ElMessage.success('属性值应用成功')
    }
    handleAttributeReferenceDialogClose()
  }
}

// 引用需求标准相关方法
const handleReferenceStandardDialogClose = () => {
  referenceStandardDialogVisible.value = false
  selectedStandards.value = []
  resetStandardSearch()
}

const resetStandardSearch = () => {
  standardSearchForm.value = {
    standardName: '',
    procurementCategory: '',
    indicatorName: ''
  }
}

const searchStandards = () => {
  // 实现搜索逻辑
  ElMessage.info('搜索功能开发中...')
}

const selectCategory = (categoryId: string) => {
  selectedCategoryId.value = categoryId
}

const getCurrentCategoryName = () => {
  const category = procurementCategories.value.find(cat => cat.id === selectedCategoryId.value)
  return category ? `${category.name} ${category.year}` : ''
}

const getSelectedCount = (categoryId: string) => {
  return selectedStandards.value.filter(std => std.categoryId === categoryId).length
}

const selectAllCurrentPage = () => {
  const currentList = currentStandardList.value
  currentList.forEach(standard => {
    const existingIndex = selectedStandards.value.findIndex(s => s.id === standard.id)
    if (existingIndex === -1) {
      selectedStandards.value.push({
        ...standard,
        categoryId: selectedCategoryId.value
      })
    }
  })
  ElMessage.success(`已选择当前页面 ${currentList.length} 条标准`)
}

const clearAllSelections = () => {
  selectedStandards.value = []
  ElMessage.info('已清空所有选择')
}

const handleStandardSelectionChange = (selection: any[]) => {
  // 移除当前分类下的所有选择
  selectedStandards.value = selectedStandards.value.filter(s => s.categoryId !== selectedCategoryId.value)

  // 添加新的选择
  selection.forEach(standard => {
    selectedStandards.value.push({
      ...standard,
      categoryId: selectedCategoryId.value
    })
  })
}

const applySelectedStandards = () => {
  if (selectedStandards.value.length === 0) {
    ElMessage.warning('请先选择需要引用的标准')
    return
  }

  // 根据当前编辑的标的类型决定添加到哪个列表
  if (currentEditingTarget.value === 'target3') {
    // 将选择的标准添加到服务要求列表中
    selectedStandards.value.forEach(standard => {
      const newId = Math.max(...serviceList.value.map(item => item.id)) + 1
      serviceList.value.push({
        id: newId,
        category: 'standard',
        serviceName: `${standard.primaryIndicator}-${standard.secondaryIndicator}`,
        requirement: standard.requirement,
        isReviewFactor: false,
        isSubstantive: false
      })
    })
    ElMessage.success(`成功引用 ${selectedStandards.value.length} 条服务标准`)
  } else {
    // 将选择的标准添加到技术要求列表中
    selectedStandards.value.forEach(standard => {
      const newId = Math.max(...technicalList.value.map(item => item.id)) + 1
      technicalList.value.push({
        id: newId,
        category: '引用标准',
        indicatorMode: 'hierarchical',
        indicatorName: '',
        primaryIndicator: standard.primaryIndicator,
        secondaryIndicator: standard.secondaryIndicator,
        requirement: standard.requirement,
        isReviewFactor: false,
        isSubstantive: false
      })
    })
    ElMessage.success(`成功引用 ${selectedStandards.value.length} 条需求标准`)
  }

  handleReferenceStandardDialogClose()
}

// 获取指标分类的标签类型
const getIndicatorCategoryType = (category: string) => {
  const categoryTypeMap: { [key: string]: string } = {
    '功能要求': 'primary',
    '性能要求': 'success',
    '安全要求': 'danger',
    '系统要求': 'info',
    '系统架构': 'info',
    '安全特性': 'danger',
    '服务质量': 'primary',
    '服务人员': 'success',
    '服务时间': 'info',
    '应急响应': 'warning',
    '服务标准': 'primary',
    '设备要求': 'info'
  }
  return categoryTypeMap[category] || 'default'
}

// 标的2编辑相关方法
// 打开标的2编辑抽屉
const openTarget2EditDrawer = () => {
  // 填充当前数据到表单
  const currentData = target2BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))

  target2EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    quantity: currentData.quantity,
    industry: currentData.industry,
    totalPrice: maxPrice // 工程类项目总价通常等于最高限制单价
  }
  target2EditDrawerVisible.value = true
}

// 关闭标的2编辑抽屉
const handleTarget2DrawerClose = () => {
  target2EditDrawerVisible.value = false
}

// 处理标的2总价变化
const handleTarget2TotalPriceChange = (value: number) => {
  console.log('标的2总价已更改为:', value)
}

// 保存标的2编辑表单
const saveTarget2EditForm = () => {
  // 这里可以添加表单验证和保存逻辑
  // 更新target2BasicInfo数据
  target2BasicInfo.value[0] = {
    name: target2EditForm.value.name,
    catalog: target2EditForm.value.catalog,
    maxPrice: `${target2EditForm.value.maxPrice}元`,
    quantity: target2EditForm.value.quantity,
    industry: target2EditForm.value.industry,
    function: 'xxxxx',
    standard: 'xxxx'
  }

  ElMessage.success('标的2基础信息保存成功')
  handleTarget2DrawerClose()
}

// 标的2政策配置相关方法
const editTarget2Policy = () => {
  policyEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的2
  currentEditingTarget.value = 'target2'
}

const configureTarget2Policy = () => {
  ElMessage.info('配置标的2政府采购政策')
  // 配置完成后更新状态
  target2Requirements.value.policy.configured = true
  target2Requirements.value.policy.count = 1
}

// 标的2技术要求配置相关方法
const editTarget2Technical = () => {
  technicalEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的2
  currentEditingTarget.value = 'target2'
}

const configureTarget2Technical = () => {
  ElMessage.info('配置标的2技术要求')
  // 配置完成后更新状态
  target2Requirements.value.technical.configured = true
  target2Requirements.value.technical.count = 2
}

// 标的2商务要求配置相关方法
const editTarget2Business = () => {
  businessEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的2
  currentEditingTarget.value = 'target2'
}

const configureTarget2Business = () => {
  ElMessage.info('配置标的2商务要求')
  // 配置完成后更新状态
  target2Requirements.value.business.configured = true
  target2Requirements.value.business.count = 3
}

// 标的4编辑相关方法
// 打开标的4编辑抽屉
const openTarget4EditDrawer = () => {
  // 填充当前数据到表单
  const currentData = target4BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))

  target4EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    quantity: currentData.quantity,
    industry: currentData.industry,
    totalPrice: maxPrice * parseInt(currentData.quantity.match(/\d+/)?.[0] || '1') // 从数量中提取数字计算总价
  }
  target4EditDrawerVisible.value = true
}

// 关闭标的4编辑抽屉
const handleTarget4DrawerClose = () => {
  target4EditDrawerVisible.value = false
}

// 处理标的4总价变化
const handleTarget4TotalPriceChange = (value: number) => {
  console.log('标的4总价已更改为:', value)
}

// 保存标的4编辑表单
const saveTarget4EditForm = () => {
  // 这里可以添加表单验证和保存逻辑
  // 更新target4BasicInfo数据
  target4BasicInfo.value[0] = {
    name: target4EditForm.value.name,
    catalog: target4EditForm.value.catalog,
    maxPrice: `${target4EditForm.value.maxPrice}元`,
    quantity: target4EditForm.value.quantity,
    industry: target4EditForm.value.industry
  }

  ElMessage.success('标的4基础信息保存成功')
  handleTarget4DrawerClose()
}

// 标的4政策配置相关方法
const editTarget4Policy = () => {
  policyEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的4
  currentEditingTarget.value = 'target4'
}

const configureTarget4Policy = () => {
  ElMessage.info('配置标的4政府采购政策')
  // 配置完成后更新状态
  target4Requirements.value.policy.configured = true
  target4Requirements.value.policy.count = 1
}

// 标的4技术要求配置相关方法
const editTarget4Technical = () => {
  technicalEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的4
  currentEditingTarget.value = 'target4'
}

const configureTarget4Technical = () => {
  ElMessage.info('配置标的4技术要求')
  // 配置完成后更新状态
  target4Requirements.value.technical.configured = true
  target4Requirements.value.technical.count = 2
}

// 标的4商务要求配置相关方法
const editTarget4Business = () => {
  businessEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的4
  currentEditingTarget.value = 'target4'
}

const configureTarget4Business = () => {
  ElMessage.info('配置标的4商务要求')
  // 配置完成后更新状态
  target4Requirements.value.business.configured = true
  target4Requirements.value.business.count = 3
}

// 标的3服务要求配置相关方法
const editTarget3Service = () => {
  serviceEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的3
  currentEditingTarget.value = 'target3'
}

const configureTarget3Service = () => {
  ElMessage.info('配置标的3服务要求')
  // 配置完成后更新状态
  target3Requirements.value.service.configured = true
  target3Requirements.value.service.count = 4
}

// 标的3商务要求配置相关方法
const editTarget3Business = () => {
  businessEditDrawerVisible.value = true
  // 设置当前编辑的标的为标的3
  currentEditingTarget.value = 'target3'
}

const configureTarget3Business = () => {
  ElMessage.info('配置标的3商务要求')
  // 配置完成后更新状态
  target3Requirements.value.business.configured = true
  target3Requirements.value.business.count = 3
}

// 标的3编辑相关方法
// 打开标的3编辑抽屉
const openTarget3EditDrawer = () => {
  // 填充当前数据到表单
  const currentData = target3BasicInfo.value[0]
  const maxPrice = parseFloat(currentData.maxPrice.replace('元', ''))

  target3EditForm.value = {
    name: currentData.name,
    catalog: currentData.catalog,
    maxPrice: maxPrice,
    servicePeriod: currentData.quantity, // 服务类项目的quantity字段对应服务期限
    industry: currentData.industry,
    serviceContent: currentData.function, // function字段对应服务内容
    propertyAddress: '北京市朝阳区建国门外大街1号', // 默认物业地址
    totalPrice: maxPrice // 服务类项目总价通常等于最高限制单价
  }
  target3EditDrawerVisible.value = true
}

// 关闭标的3编辑抽屉
const handleTarget3DrawerClose = () => {
  target3EditDrawerVisible.value = false
}

// 处理标的3总价变化
const handleTarget3TotalPriceChange = (value: number) => {
  console.log('标的3总价已更改为:', value)
}

// 保存标的3编辑表单
const saveTarget3EditForm = () => {
  // 这里可以添加表单验证和保存逻辑
  // 更新target3BasicInfo数据
  target3BasicInfo.value[0] = {
    name: target3EditForm.value.name,
    catalog: target3EditForm.value.catalog,
    maxPrice: `${target3EditForm.value.maxPrice}元`,
    quantity: target3EditForm.value.servicePeriod,
    industry: target3EditForm.value.industry,
    function: target3EditForm.value.serviceContent,
    standard: '服务质量标准'
  }

  ElMessage.success('标的3基础信息保存成功')
  handleTarget3DrawerClose()
}

// 物业管理内容清单相关方法
const openPropertyManagementDialog = () => {
  propertyManagementDialogVisible.value = true
}

const closePropertyManagementDialog = () => {
  propertyManagementDialogVisible.value = false
}

// 服务内容清单相关方法
const addServiceItem = () => {
  const newId = Math.max(...serviceContentList.value.map(item => item.id)) + 1
  serviceContentList.value.push({
    id: newId,
    name: '',
    description: '',
    standard: ''
  })
}

const removeServiceItem = (index: number) => {
  serviceContentList.value.splice(index, 1)
}

// 采购人内容提供清单相关方法
const addProcurementItem = () => {
  const newId = Math.max(...procurementContentList.value.map(item => item.id)) + 1
  procurementContentList.value.push({
    id: newId,
    category: '',
    name: '',
    description: '',
    timing: '',
    remark: ''
  })
}

const removeProcurementItem = (index: number) => {
  procurementContentList.value.splice(index, 1)
}

// 保持原有方法用于兼容性
const addPropertyManagementItem = () => {
  addServiceItem()
}

const removePropertyManagementItem = (index: number) => {
  removeServiceItem(index)
}

const savePropertyManagementList = () => {
  ElMessage.success('物业管理内容清单保存成功')
  closePropertyManagementDialog()
}
</script>

<style scoped>
.procurement-file-config {
  min-height: 100vh;
  background-color: #f0f2f5;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 顶部项目信息栏 */
.project-info-header {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.project-info-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-icon {
  font-size: 18px;
}

.project-title {
  font-size: 16px;
  font-weight: 600;
}

.project-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.project-info-right {
  display: flex;
  gap: 12px;
}

/* 主导航Tab */
.main-tabs-section {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  width: 100%;
  box-sizing: border-box;
}

.main-tabs {
  width: 100%;
  margin-left: 20px;
}

.main-tabs .el-tabs__header {
  margin: 0;
  border-bottom: none;
}

.main-tabs .el-tabs__nav-wrap {
  padding: 0 20px;
}

.main-tabs .el-tabs__item {
  padding: 20px 30px;
  font-size: 14px;
  border: none;
  background: transparent;
}

.main-tabs .el-tabs__item.is-active {
  background: #e6f7ff;
  color: #1890ff;
  border-bottom: 3px solid #1890ff;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.tab-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

.main-tabs .el-tabs__item.is-active .tab-circle {
  background: #1890ff;
  color: white;
}

.check-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  color: #52c41a;
  background: white;
  border-radius: 50%;
  font-size: 12px;
  padding: 2px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #d9d9d9;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 8px;
}

.step-item.completed .step-circle {
  background: #52c41a;
}

.step-item.active .step-circle {
  background: #1890ff;
}

.step-content {
  text-align: center;
  position: relative;
}

.step-title {
  font-size: 12px;
  color: #666;
}

.check-icon {
  position: absolute;
  top: -20px;
  right: -8px;
  color: #52c41a;
  background: white;
  border-radius: 50%;
  font-size: 12px;
}

/* 采购文件配置页面专用样式 */

/* 主要内容区域 */
.procurement-main-content {
  padding: 20px;
  display:flex;
  flex-direction: column;
  gap: 0;
  width: 100%;
  max-width: none;
  min-height: calc(100vh - 200px);
}

/* 基础信息区域 */
.procurement-basic-info-section {
  margin-bottom: 0;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.procurement-basic-info-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: none;
  margin: 0;
}

.procurement-basic-info-content {
  padding: 16px;
}

.procurement-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.procurement-label {
  font-weight: 500;
  color: #333;
}

.basic-info-content {
  padding: 10px 0;
}

.basic-info-content .info-item {
  display: flex;
  align-items: center;
}

.basic-info-content .label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 分包信息Tab区域 */
.procurement-package-tabs-section {
  flex: 1;
  padding: 20px;
  background: white;
}

.procurement-package-tabs {
  background: white;
  border-radius: 0;
  box-shadow: none;
  width: 100%;
  border: none;
}

.procurement-package-tabs .el-tabs__header {
  margin: 0;
  background: transparent;
  border-radius: 0;
  border-bottom: 1px solid #e9ecef;
}

.procurement-package-tabs .el-tabs__content {
  padding: 0;
}

/* 分包信息展示 */
.procurement-package-info-display {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.procurement-package-info-card {
  box-shadow: none;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.procurement-package-info-grid {
  padding: 20px;
}

.procurement-info-row {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.procurement-info-row:last-child {
  margin-bottom: 0;
}

.procurement-info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.procurement-info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.procurement-info-value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.procurement-info-note {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.package-info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.info-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-note {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 配置表单区域 */
.procurement-config-form-section {
  margin-top: 0;
  padding: 20px;
  background: white;
}

.procurement-config-tabs {
  background: white;
}

.procurement-config-tabs .el-tabs__header {
  margin: 0 0 24px 0;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  padding: 0 16px;
  border-radius: 8px 8px 0 0;
}

.procurement-config-card {
  box-shadow: none;
  border: 1px solid #e4e7ed;
  width: 100%;
  border-radius: 8px;
}

/* Tab内容区域 */
.procurement-tab-content {
  width: 100%;
  height: calc(100vh - 200px);
  background: white;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Tab内容区域滚动条样式 */
.procurement-tab-content::-webkit-scrollbar {
  width: 8px;
}

.procurement-tab-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.procurement-tab-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.procurement-tab-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.procurement-content-card {
  margin: 0;
  box-shadow: none;
  border: none;
  border-radius: 0;
  height: 100%;
}

.procurement-content-body {
  padding: 20px 20px;
  text-align: center;
  color: #666;
  font-size: 16px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.procurement-card-header {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .procurement-form-row {
    gap: 24px;
  }

  .procurement-form-content {
    padding: 24px;
  }

  .procurement-requirements-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media (max-width: 900px) {
  .procurement-requirements-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .procurement-main-content {
    padding: 0;
  }

  .procurement-basic-info-section,
  .procurement-package-tabs-section,
  .procurement-config-form-section,
  .procurement-package-info-display {
    padding: 16px;
  }

  .procurement-form-row {
    flex-direction: column;
    gap: 20px;
  }

  .procurement-info-row {
    flex-direction: column;
    gap: 16px;
  }

  .procurement-info-item {
    min-width: auto;
  }

  .project-info-header {
    padding: 12px 16px;
  }

  .main-tabs .el-tabs__nav-wrap {
    padding: 0 16px;
  }

  .main-tabs .el-tabs__item {
    padding: 16px 20px;
  }

  .procurement-content-body {
    padding: 40px 20px;
  }

  .procurement-form-content {
    padding: 20px;
  }

  .procurement-requirements-section {
    padding: 16px;
    height: calc(100vh - 160px);
    -webkit-overflow-scrolling: touch;
  }

  .procurement-requirements-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .procurement-target-basic-info {
    padding: 16px;
  }

  .procurement-target-requirements {
    padding: 16px;
  }

  .procurement-requirement-content {
    padding: 16px;
    min-height: 80px;
  }

  .procurement-info-table {
    font-size: 12px;
  }

  .procurement-info-table .el-table__header th {
    padding: 8px 4px;
  }

  .procurement-info-table .el-table__body td {
    padding: 8px 4px;
  }
}

/* 表单样式 */
.procurement-form-content {
  padding: 32px;
  width: 100%;
  box-sizing: border-box;
  background: white;
}

.procurement-form-row {
  display: flex;
  gap: 32px;
  margin-bottom: 28px;
  width: 100%;
  align-items: flex-start;
}

.procurement-form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
}

.procurement-form-item.procurement-full-width {
  flex: 2.5;
}

.procurement-required {
  position: relative;
  font-weight: 500;
  color: #333;
}

.procurement-required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

.procurement-help-icon {
  color: #999;
  cursor: help;
  margin-left: 8px;
}

.procurement-unit-text,
.procurement-char-count {
  font-size: 12px;
  color: #999;
  align-self: flex-end;
}

.procurement-price-input,
.procurement-period-input,
.procurement-desc-textarea,
.procurement-remark-textarea {
  width: 100%;
}

/* 采购需求样式 */
.procurement-requirements-section {
  padding: 20px;
  background: white;
  height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.procurement-requirements-section::-webkit-scrollbar {
  width: 8px;
}

.procurement-requirements-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.procurement-requirements-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.procurement-requirements-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.procurement-requirements-tabs {
  background: white;
}

.procurement-requirements-tabs .el-tabs__header {
  margin: 0 0 20px 0;
  border-bottom: 2px solid #e4e7ed;
}

.procurement-requirements-tabs .el-tabs__item {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 24px;
}

.procurement-requirements-tabs .el-tabs__item.is-active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.procurement-targets-list {
  width: 100%;
}

.procurement-target-item {
  margin-bottom: 40px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.procurement-target-item:last-child {
  margin-bottom: 0;
}

.procurement-target-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.procurement-target-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.procurement-target-basic-info {
  padding: 20px;
  background: white;
}

.procurement-info-table {
  width: 100%;
  margin-bottom: 0;
}

.procurement-info-table .el-table__header th {
  background: #fafafa;
  color: #333;
  font-weight: 600;
}

.required-field {
  color: #ff4d4f;
  position: relative;
}

.required-field::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

.procurement-target-requirements {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.procurement-requirements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.procurement-requirement-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.procurement-requirement-header {
  background: #f0f0f0;
  padding: 12px 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
}

.procurement-requirement-header::before,
.procurement-requirement-header::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-style: solid;
}

.procurement-requirement-header::before {
  left: 0;
  border-left: 20px solid #f8f9fa;
  border-top: 22px solid transparent;
  border-bottom: 22px solid transparent;
}

.procurement-requirement-header::after {
  right: 0;
  border-right: 20px solid #f8f9fa;
  border-top: 22px solid transparent;
  border-bottom: 22px solid transparent;
}

.procurement-requirement-content {
  padding: 20px;
  min-height: 100px;
  color: #666;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

/* 已配置状态样式 */
.requirement-configured {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.configured-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #52c41a;
  font-weight: 500;
}

.configured-icon {
  font-size: 18px;
  color: #52c41a;
}

.configured-text {
  font-size: 14px;
}

/* 未配置状态样式 */
.requirement-unconfigured {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.unconfigured-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #999;
  font-weight: 500;
}

.unconfigured-icon {
  font-size: 18px;
  color: #999;
}

.unconfigured-text {
  font-size: 14px;
}

.procurement-empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
  font-size: 16px;
}

/* 标的编辑抽屉样式 */
.target-edit-form {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.target-edit-form .el-form {
  flex: 1;
}

.target-edit-form .el-form-item {
  margin-bottom: 24px;
}

.target-edit-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

.target-edit-form .el-input,
.target-edit-form .el-select,
.target-edit-form .el-input-number {
  width: 100%;
}

.drawer-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.drawer-footer .el-button {
  min-width: 80px;
}

/* 政府采购政策编辑抽屉样式 */
.policy-edit-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.policy-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.policy-count {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.policy-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.policy-table {
  width: 100%;
}

.policy-table .el-table__header th {
  background-color: #f5f7fa;
  font-weight: 600;
}

.policy-table .el-table__row {
  height: auto;
}

.policy-table .el-table__cell {
  padding: 8px 0;
}

.policy-drawer-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.policy-drawer-footer .el-button {
  min-width: 80px;
}

/* 商务要求编辑抽屉样式 */
.business-edit-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.business-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.business-count {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.business-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.business-table {
  width: 100%;
}

.business-table .el-table__header th {
  background-color: #f5f7fa;
  font-weight: 600;
}

.business-table .el-table__row {
  height: auto;
}

.business-table .el-table__cell {
  padding: 8px 0;
}

.business-drawer-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.business-drawer-footer .el-button {
  min-width: 80px;
}

/* 技术要求编辑抽屉样式 */
.technical-edit-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.technical-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.technical-count {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.technical-header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.technical-header-actions .el-button {
  margin-left: 0;
}

.technical-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.technical-table {
  width: 100%;
}

.technical-table .el-table__header th {
  background-color: #f5f7fa;
  font-weight: 600;
}

.technical-table .el-table__row {
  height: auto;
}

.technical-table .el-table__cell {
  padding: 8px 0;
}

.technical-drawer-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.technical-drawer-footer .el-button {
  min-width: 80px;
}

/* 指标名称配置样式 */
.indicator-name-container {
  width: 100%;
}

.indicator-mode-switch {
  margin-bottom: 8px;
}

.indicator-mode-switch .el-radio-group {
  width: 100%;
}

.indicator-mode-switch .el-radio-button__inner {
  padding: 4px 8px;
  font-size: 12px;
}

.single-indicator {
  width: 100%;
}

.hierarchical-indicator {
  width: 100%;
}

.indicator-level-row {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.primary-indicator {
  flex: 1;
  min-width: 0;
}

.secondary-indicator {
  flex: 1;
  min-width: 0;
}

.indicator-separator {
  color: #909399;
  font-weight: 500;
  font-size: 12px;
  flex-shrink: 0;
  padding: 0 2px;
}

/* 表格内的指标名称样式优化 */
.technical-table .indicator-name-container {
  padding: 4px 0;
}

.technical-table .indicator-mode-switch {
  margin-bottom: 6px;
}

/* 分类编辑对话框样式 */
.category-edit-container {
  padding: 0;
}

.category-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.category-count {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.category-list {
  margin-bottom: 24px;
  max-height: 300px;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.category-item:last-child {
  margin-bottom: 0;
}

.category-input {
  flex: 0 0 150px;
}

.category-description {
  flex: 1;
  min-width: 0;
}

.category-delete-btn {
  flex-shrink: 0;
}

.category-preset-section {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.preset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preset-header span {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.preset-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preset-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.preset-tag:hover {
  background-color: #409eff;
  color: white;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-item label {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.form-item label.required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

.amount-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-value {
  font-weight: 600;
  color: #262626;
}

.amount-unit {
  color: #8c8c8c;
  font-size: 12px;
}

.char-count {
  font-size: 12px;
  color: #8c8c8c;
  text-align: right;
}

.unit-text {
  font-size: 12px;
  color: #8c8c8c;
}

.time-separator {
  margin: 0 8px;
  color: #8c8c8c;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 30px 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.help-icon {
  color: #8c8c8c;
  cursor: help;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-time-picker) {
  width: 100px;
}

/* 技术要求操作按钮样式 */
.technical-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.technical-actions .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

/* 服务要求操作按钮样式 */
.service-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.service-actions .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

/* 对表国标要求弹窗样式 */
.standard-comparison-container {
  padding: 0;
}

.comparison-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.current-requirement h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.requirement-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.requirement-info .info-item {
  display: flex;
  margin-bottom: 8px;
}

.requirement-info .info-item:last-child {
  margin-bottom: 0;
}

.requirement-info .label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.requirement-info .value {
  color: #333;
  flex: 1;
}

.comparison-content {
  margin-bottom: 20px;
}

.comparison-table .standard-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.comparison-table .standard-name {
  font-weight: 500;
  color: #333;
}

.comparison-table .standard-requirement {
  color: #666;
  line-height: 1.4;
}

.difference-analysis {
  text-align: center;
}

.difference-analysis .el-tag {
  margin-bottom: 8px;
}

.difference-desc {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.comparison-table .suggestion {
  color: #52c41a;
  font-size: 12px;
  line-height: 1.4;
}

.comparison-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 属性值参考弹窗样式 */
.attribute-reference-container {
  padding: 0;
}

.reference-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.current-indicator h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.indicator-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.indicator-info .info-item {
  display: flex;
  margin-bottom: 8px;
}

.indicator-info .info-item:last-child {
  margin-bottom: 0;
}

.indicator-info .label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.indicator-info .value {
  color: #333;
  flex: 1;
}

.reference-content {
  margin-bottom: 20px;
}

.common-values .value-category {
  margin-bottom: 20px;
}

.common-values .value-category:last-child {
  margin-bottom: 0;
}

.common-values h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.value-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.value-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.value-tag:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.reference-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 引用需求标准弹窗样式 */
.reference-standard-dialog {
  --el-dialog-padding-primary: 0;
}

.reference-standard-container {
  display: flex;
  flex-direction: column;
  height: 700px;
  padding: 0;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-item label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 80px;
}

.search-input {
  width: 160px;
}

.search-select {
  width: 180px;
}

.search-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.main-content-section {
  display: flex;
  flex: 1;
  min-height: 0;
}

.left-sidebar {
  width: 280px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

.sidebar-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.category-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.category-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.category-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.category-year {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.category-code {
  font-size: 12px;
  color: #999;
}

.category-count {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

.selected-category-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.total-count {
  font-size: 12px;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.standards-table-container {
  flex: 1;
  padding: 0 20px 20px 20px;
  overflow: hidden;
}

.standards-table {
  height: 100%;
}

.indicator-info {
  text-align: left;
}

.primary-indicator {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.secondary-indicator {
  font-size: 12px;
  color: #666;
}

.requirement-text {
  line-height: 1.4;
  color: #333;
}

.usage-description {
  line-height: 1.4;
  color: #666;
  font-size: 13px;
}

.other-attributes {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attribute-tag {
  font-size: 11px;
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
}

.selected-info {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

/* 指标分类标签样式 */
.category-tag {
  font-weight: 500;
  border-radius: 4px;
}

.category-tag.el-tag--primary {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.category-tag.el-tag--success {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.category-tag.el-tag--danger {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.category-tag.el-tag--info {
  background: #f0f0f0;
  border-color: #d9d9d9;
  color: #595959;
}

/* 总价计算提示样式 */
.price-calculation-hint {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
}

.hint-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 标的编辑表单样式优化 */
.target-edit-form {
  padding: 20px;
}

.target-edit-form .el-form-item {
  margin-bottom: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

/* 物业管理内容设置样式 */
.property-management-setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.setting-info {
  flex: 1;
}

.setting-status {
  font-size: 14px;
  color: #28a745;
  font-weight: 500;
}

.setting-status-empty {
  font-size: 14px;
  color: #6c757d;
}

/* 物业管理内容清单弹窗样式 */
.property-management-container {
  padding: 0;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.header-info {
  flex: 1;
}

.info-text {
  font-size: 14px;
  color: #6c757d;
}

.management-list {
  max-height: 400px;
  overflow-y: auto;
}

.management-list .el-table {
  font-size: 13px;
}

.management-list .el-input__inner {
  font-size: 13px;
}

.management-list .el-select {
  font-size: 13px;
}

/* 管理内容标签页样式 */
.management-tabs {
  margin-top: -10px;
}

.management-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.management-tabs .el-tab-pane {
  padding: 0;
}

.tab-content {
  padding: 0;
}

.tab-content .management-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.tab-content .management-list {
  max-height: 350px;
  overflow-y: auto;
}
</style>
