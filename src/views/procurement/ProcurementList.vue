<template>
  <div class="procurement-list">
    <!-- 搜索筛选区 -->
    <div class="search-section">
      <el-card>
        <div class="search-form">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-input
                v-model="searchForm.projectNumber"
                placeholder="项目编号"
                clearable
              />
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="searchForm.projectName"
                placeholder="项目名称"
                clearable
              />
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="searchForm.procurementMethod"
                placeholder="采购方式"
                clearable
                style="width: 100%"
              >
                <el-option label="公开招标" value="公开招标" />
                <el-option label="竞争性谈判" value="竞争性谈判" />
                <el-option label="询价采购" value="询价采购" />
                <el-option label="单一来源" value="单一来源" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <div class="search-buttons">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
                <el-button @click="handleReset">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <div class="table-header">
          <div class="table-title">
            <span>项目列表</span>
            <span class="table-count">共 {{ tableData.length }} 条</span>
          </div>
          <div class="table-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增项目
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              生成采购文件导出数据
            </el-button>
          </div>
        </div>

        <el-table
          :data="filteredData"
          style="width: 100%"
          stripe
          border
          :header-cell-style="{ background: '#fafafa', color: '#262626' }"
        >
          <el-table-column prop="projectNumber" label="项目编号" width="180" />
          <el-table-column prop="projectName" label="项目名称" min-width="200">
            <template #default="scope">
              <el-link type="primary" @click="handleView(scope.row)">
                {{ scope.row.projectName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="purchaser" label="采购人" width="150" />
          <el-table-column prop="procurementMethod" label="采购方式" width="120">
            <template #default="scope">
              <el-tag
                :type="getProcurementMethodType(scope.row.procurementMethod)"
                size="small"
              >
                {{ scope.row.procurementMethod }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="项目金额(元)" width="140" align="right">
            <template #default="scope">
              <span class="amount-text">{{ formatAmount(scope.row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="bidOpeningTime" label="开标时间" width="180" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleRowExport(scope.row)"
              >
                生成采购文件导出数据
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleSelectSuppliers(scope.row)"
              >
                从供应商库选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="tableData.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- CA绑定检查弹窗 -->
    <el-dialog
      v-model="caCheckDialogVisible"
      title="导出确认"
      width="900px"
      :before-close="handleCaCheckDialogClose"
    >
      <div class="ca-check-container">
        <div class="warning-message">
          <el-icon class="warning-icon" color="#E6A23C"><WarningFilled /></el-icon>
          <div class="message-content">
            <p class="main-message">下列供应商未绑定CA</p>
            <p class="sub-message">导出后，未绑定CA的供应商不可参与后续投标、开评标等工作</p>
          </div>
        </div>

        <div class="supplier-list" v-if="unboundSuppliers.length > 0">
          <div class="list-header">
            <span class="list-title">未绑定CA的供应商列表</span>
            <span class="supplier-count">共 {{ unboundSuppliers.length }} 家</span>
          </div>
          <el-table
            :data="unboundSuppliers"
            style="width: 100%"
            max-height="300"
            border
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="供应商名称" width="180" />
            <el-table-column prop="creditCode" label="统一信用代码" width="180" />
            <el-table-column prop="contactPerson" label="联系人" width="100" />
            <el-table-column prop="contactPhone" label="联系电话" width="130" />
            <el-table-column prop="participateInCollection" label="是否参加征集" width="120" align="center">
              <template #default="scope">
                <el-tag
                  :type="scope.row.participateInCollection ? 'success' : 'info'"
                  size="small"
                >
                  {{ scope.row.participateInCollection ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="no-unbound-suppliers" v-else>
          <el-icon class="success-icon" color="#67C23A"><CircleCheckFilled /></el-icon>
          <span class="success-message">所有供应商均已绑定CA，可以正常导出</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCaCheckDialogClose">取消</el-button>
          <el-button type="primary" @click="confirmExport">
            {{ unboundSuppliers.length > 0 ? '确认导出' : '开始导出' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 供应商选择弹窗 -->
    <el-dialog
      v-model="supplierSelectDialogVisible"
      title="从供应商库选择"
      width="1200px"
      top="5vh"
      :before-close="handleSupplierSelectDialogClose"
      class="supplier-select-dialog"
    >
      <div class="supplier-select-container">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-card>
            <div class="filter-form">
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-select
                    v-model="supplierFilter.region"
                    placeholder="请选择地区"
                    clearable
                    style="width: 100%"
                  >
                    <el-option label="北京市" value="北京市" />
                    <el-option label="上海市" value="上海市" />
                    <el-option label="广东省" value="广东省" />
                    <el-option label="浙江省" value="浙江省" />
                    <el-option label="江苏省" value="江苏省" />
                    <el-option label="山东省" value="山东省" />
                    <el-option label="四川省" value="四川省" />
                    <el-option label="湖北省" value="湖北省" />
                    <el-option label="河南省" value="河南省" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-select
                    v-model="supplierFilter.qualification"
                    placeholder="请选择资质"
                    clearable
                    style="width: 100%"
                  >
                    <el-option label="建筑工程施工总承包" value="建筑工程施工总承包" />
                    <el-option label="市政公用工程施工总承包" value="市政公用工程施工总承包" />
                    <el-option label="机电工程施工总承包" value="机电工程施工总承包" />
                    <el-option label="园林绿化工程专业承包" value="园林绿化工程专业承包" />
                    <el-option label="信息系统集成及服务" value="信息系统集成及服务" />
                    <el-option label="安全技术防范系统设计施工维护" value="安全技术防范系统设计施工维护" />
                    <el-option label="物业服务企业" value="物业服务企业" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-input
                    v-model="supplierFilter.keyword"
                    placeholder="搜索供应商名称"
                    clearable
                  />
                </el-col>
                <el-col :span="6">
                  <div class="filter-buttons">
                    <el-button type="primary" @click="handleSupplierSearch">
                      <el-icon><Search /></el-icon>
                      查询
                    </el-button>
                    <el-button @click="handleSupplierFilterReset">
                      <el-icon><Refresh /></el-icon>
                      重置
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>

        <!-- 供应商列表 -->
        <div class="supplier-list-section">
          <el-card>
            <div class="list-header">
              <div class="list-title">
                <span>供应商列表</span>
                <span class="list-count">共 {{ filteredSuppliers.length }} 家</span>
              </div>
              <div class="selection-info">
                <span class="selected-count">已选择 {{ selectedSuppliers.length }} 家供应商</span>
                <el-button
                  type="danger"
                  size="small"
                  @click="clearSelectedSuppliers"
                  :disabled="selectedSuppliers.length === 0"
                >
                  清空选择
                </el-button>
              </div>
            </div>

            <el-table
              ref="supplierTableRef"
              :data="paginatedSuppliers"
              style="width: 100%"
              max-height="400"
              stripe
              border
              @selection-change="handleSupplierSelectionChange"
              :header-cell-style="{ background: '#fafafa', color: '#262626' }"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="name" label="供应商名称" min-width="250" />
              <el-table-column prop="creditCode" label="统一信用代码" width="200" />
              <el-table-column prop="contactPerson" label="联系人" width="120" />
              <el-table-column prop="contactPhone" label="联系电话" width="140" />
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="supplierCurrentPage"
                v-model:page-size="supplierPageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="filteredSuppliers.length"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSupplierSizeChange"
                @current-change="handleSupplierCurrentChange"
              />
            </div>
          </el-card>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleSupplierSelectDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="proceedToTargetSelection"
            :disabled="selectedSuppliers.length === 0"
          >
            下一步：选择标项（{{ selectedSuppliers.length }}）
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 标项选择弹窗 -->
    <el-dialog
      v-model="targetSelectDialogVisible"
      title="选择标项"
      width="800px"
      top="10vh"
      :before-close="handleTargetSelectDialogClose"
      class="target-select-dialog"
    >
      <div class="target-select-container">
        <div class="selected-suppliers-info">
          <div class="info-header">
            <el-icon class="info-icon" color="#409EFF"><User /></el-icon>
            <span class="info-title">已选择供应商</span>
            <span class="supplier-count">{{ selectedSuppliers.length }}家</span>
          </div>
          <div class="suppliers-list">
            <el-tag
              v-for="supplier in selectedSuppliers"
              :key="supplier.id"
              size="small"
              class="supplier-tag"
            >
              {{ supplier.name }}
            </el-tag>
          </div>
        </div>

        <div class="target-selection-section">
          <div class="section-header">
            <el-icon class="section-icon" color="#67C23A"><Flag /></el-icon>
            <span class="section-title">请选择供应商参与的标项</span>
          </div>

          <div class="targets-list">
            <el-checkbox-group v-model="selectedTargets">
              <div class="target-item" v-for="target in projectTargets" :key="target.id">
                <el-checkbox :label="target.id" class="target-checkbox">
                  <div class="target-content">
                    <div class="target-name">{{ target.name }}</div>
                    <div class="target-description">{{ target.description }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="backToSupplierSelection">上一步</el-button>
          <el-button @click="handleTargetSelectDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="confirmFinalSelection"
            :disabled="selectedTargets.length === 0"
          >
            确认选择（{{ selectedTargets.length }}个标项）
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Refresh, Plus, Download, WarningFilled, CircleCheckFilled, User, Flag } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 搜索表单
const searchForm = ref({
  projectNumber: '',
  projectName: '',
  procurementMethod: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// CA绑定检查弹窗相关
const caCheckDialogVisible = ref(false)
const currentExportProject = ref(null)
const unboundSuppliers = ref([])

// 供应商选择弹窗相关
const supplierSelectDialogVisible = ref(false)
const currentSelectProject = ref(null)
const selectedSuppliers = ref([])
const supplierTableRef = ref()

// 供应商筛选条件
const supplierFilter = ref({
  region: '',
  qualification: '',
  keyword: ''
})

// 供应商分页
const supplierCurrentPage = ref(1)
const supplierPageSize = ref(20)

// 标项选择弹窗相关
const targetSelectDialogVisible = ref(false)
const selectedTargets = ref([])

// 项目标项数据
const projectTargets = ref([
  {
    id: 'target1',
    name: '标的1（货物类）',
    description: '台式计算机等办公设备采购'
  },
  {
    id: 'target2',
    name: '标的2（项目采购-工程类）',
    description: '办公用房工程建设项目'
  },
  {
    id: 'target3',
    name: '标的3（物业服务类）',
    description: '物业管理服务项目'
  },
  {
    id: 'target4',
    name: '标的4（服务类-非物业）',
    description: '安全设备相关服务'
  }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    projectNumber: 'BUYP-LANNJM(2023)3B0700201-1-20250770001',
    projectName: '某地工程建设项目2025070770001',
    purchaser: '吉林市水务集团有限公司',
    procurementMethod: '竞争性谈判',
    amount: 60000024.68,
    bidOpeningTime: '2025-07-31 11:43:00',
    status: '已完成',
    suppliers: [
      { name: '北京建设工程有限公司', creditCode: '91110000123456789A', contactPerson: '张三', contactPhone: '13800138001', registrationTime: '2024-01-15', caBinding: true, participateInCollection: true },
      { name: '上海基础设施建设集团', creditCode: '91310000987654321B', contactPerson: '李四', contactPhone: '13800138002', registrationTime: '2024-02-20', caBinding: false, participateInCollection: false },
      { name: '广州市政工程公司', creditCode: '91440000456789123C', contactPerson: '王五', contactPhone: '13800138003', registrationTime: '2024-03-10', caBinding: true, participateInCollection: true }
    ]
  },
  {
    id: 2,
    projectNumber: 'BUYP-LANNJM(2023)3B0250708714-1-20250760005',
    projectName: '某地道路景观工程项目20250760005',
    purchaser: '吉林市水务集团有限公司',
    procurementMethod: '公开招标',
    amount: 20000000.00,
    bidOpeningTime: '-',
    status: '待开标',
    suppliers: [
      { name: '深圳园林景观有限公司', creditCode: '91440300789123456D', contactPerson: '赵六', contactPhone: '13800138004', registrationTime: '2024-01-25', caBinding: false, participateInCollection: true },
      { name: '杭州绿化工程集团', creditCode: '91330100654321987E', contactPerson: '钱七', contactPhone: '13800138005', registrationTime: '2024-02-15', caBinding: false, participateInCollection: false },
      { name: '成都市政建设公司', creditCode: '91510100321654987F', contactPerson: '孙八', contactPhone: '13800138006', registrationTime: '2024-03-05', caBinding: true, participateInCollection: true },
      { name: '重庆道路工程有限公司', creditCode: '91500000147258369G', contactPerson: '周九', contactPhone: '13800138007', registrationTime: '2024-03-20', caBinding: true, participateInCollection: true }
    ]
  },
  {
    id: 3,
    projectNumber: '5678-20250770002',
    projectName: '某个系统集成项目',
    purchaser: '吉林市水务集团有限公司',
    procurementMethod: '竞争性谈判',
    amount: 1000.00,
    bidOpeningTime: '-',
    status: '待开标',
    suppliers: [
      { name: '华为技术有限公司', creditCode: '91440300708461136H', contactPerson: '刘十', contactPhone: '13800138008', registrationTime: '2024-01-10', caBinding: true, participateInCollection: true },
      { name: '中兴通讯股份有限公司', creditCode: '91440300192439974I', contactPerson: '陈十一', contactPhone: '13800138009', registrationTime: '2024-02-05', caBinding: true, participateInCollection: true },
      { name: '浪潮集团有限公司', creditCode: '91370100163500026J', contactPerson: '林十二', contactPhone: '13800138010', registrationTime: '2024-02-28', caBinding: true, participateInCollection: false }
    ]
  },
  {
    id: 4,
    projectNumber: 'BUYP-LANNJM(2023)3B0275-1-001',
    projectName: '某地-分散采购-个人征信查询服务332725',
    purchaser: '吉林市水务集团有限公司',
    procurementMethod: '竞争性谈判',
    amount: 30000000.00,
    bidOpeningTime: '-',
    status: '已完成',
    suppliers: [
      { name: '中国人民银行征信中心', creditCode: '91100000100000001K', contactPerson: '张经理', contactPhone: '13800138011', registrationTime: '2024-01-05', caBinding: true, participateInCollection: true },
      { name: '芝麻信用管理有限公司', creditCode: '91330100MA27XF6L5L', contactPerson: '李经理', contactPhone: '13800138012', registrationTime: '2024-01-20', caBinding: false, participateInCollection: true }
    ]
  },
  {
    id: 5,
    projectNumber: 'A20241230-1-12',
    projectName: 'A20241230012',
    purchaser: '吉林市水务集团有限公司',
    procurementMethod: '公开招标',
    amount: 20000000.00,
    bidOpeningTime: '2025-07-31 12:00:00',
    status: '已完成',
    suppliers: [
      { name: '东软集团股份有限公司', creditCode: '91210100124000004M', contactPerson: '王经理', contactPhone: '13800138013', registrationTime: '2024-02-10', caBinding: true, participateInCollection: true },
      { name: '用友网络科技股份有限公司', creditCode: '91110000633674020N', contactPerson: '赵经理', contactPhone: '13800138014', registrationTime: '2024-02-25', caBinding: true, participateInCollection: false },
      { name: '金蝶国际软件集团有限公司', creditCode: '91440300279467732O', contactPerson: '钱经理', contactPhone: '13800138015', registrationTime: '2024-03-01', caBinding: false, participateInCollection: true }
    ]
  }
])

// 供应商库数据
const supplierDatabase = ref([
  {
    id: 1,
    name: '北京建设工程有限公司',
    creditCode: '91110000123456789A',
    region: '北京市',
    qualification: '建筑工程施工总承包',
    contactPerson: '张三',
    contactPhone: '13800138001',
    registrationTime: '2024-01-15',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 2,
    name: '上海基础设施建设集团',
    creditCode: '91310000987654321B',
    region: '上海市',
    qualification: '市政公用工程施工总承包',
    contactPerson: '李四',
    contactPhone: '13800138002',
    registrationTime: '2024-02-20',
    caBinding: false,
    participateInCollection: false
  },
  {
    id: 3,
    name: '广州市政工程公司',
    creditCode: '91440000456789123C',
    region: '广东省',
    qualification: '市政公用工程施工总承包',
    contactPerson: '王五',
    contactPhone: '13800138003',
    registrationTime: '2024-03-10',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 4,
    name: '深圳园林景观有限公司',
    creditCode: '91440300789123456D',
    region: '广东省',
    qualification: '园林绿化工程专业承包',
    contactPerson: '赵六',
    contactPhone: '13800138004',
    registrationTime: '2024-01-25',
    caBinding: false,
    participateInCollection: true
  },
  {
    id: 5,
    name: '杭州绿化工程集团',
    creditCode: '91330100654321987E',
    region: '浙江省',
    qualification: '园林绿化工程专业承包',
    contactPerson: '钱七',
    contactPhone: '13800138005',
    registrationTime: '2024-02-15',
    caBinding: false,
    participateInCollection: false
  },
  {
    id: 6,
    name: '华为技术有限公司',
    creditCode: '91440300708461136H',
    region: '广东省',
    qualification: '信息系统集成及服务',
    contactPerson: '刘十',
    contactPhone: '13800138008',
    registrationTime: '2024-01-10',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 7,
    name: '中兴通讯股份有限公司',
    creditCode: '91440300192439974I',
    region: '广东省',
    qualification: '信息系统集成及服务',
    contactPerson: '陈十一',
    contactPhone: '13800138009',
    registrationTime: '2024-02-05',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 8,
    name: '浪潮集团有限公司',
    creditCode: '91370100163500026J',
    region: '山东省',
    qualification: '信息系统集成及服务',
    contactPerson: '林十二',
    contactPhone: '13800138010',
    registrationTime: '2024-02-28',
    caBinding: true,
    participateInCollection: false
  },
  {
    id: 9,
    name: '东软集团股份有限公司',
    creditCode: '91210100124000004M',
    region: '辽宁省',
    qualification: '信息系统集成及服务',
    contactPerson: '王经理',
    contactPhone: '13800138013',
    registrationTime: '2024-02-10',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 10,
    name: '用友网络科技股份有限公司',
    creditCode: '91110000633674020N',
    region: '北京市',
    qualification: '信息系统集成及服务',
    contactPerson: '赵经理',
    contactPhone: '13800138014',
    registrationTime: '2024-02-25',
    caBinding: true,
    participateInCollection: false
  },
  {
    id: 11,
    name: '成都市政建设公司',
    creditCode: '91510100321654987F',
    region: '四川省',
    qualification: '市政公用工程施工总承包',
    contactPerson: '孙八',
    contactPhone: '13800138006',
    registrationTime: '2024-03-05',
    caBinding: true,
    participateInCollection: true
  },
  {
    id: 12,
    name: '重庆道路工程有限公司',
    creditCode: '91500000147258369G',
    region: '重庆市',
    qualification: '市政公用工程施工总承包',
    contactPerson: '周九',
    contactPhone: '13800138007',
    registrationTime: '2024-03-20',
    caBinding: true,
    participateInCollection: true
  }
])

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value
  
  if (searchForm.value.projectNumber) {
    data = data.filter(item => 
      item.projectNumber.toLowerCase().includes(searchForm.value.projectNumber.toLowerCase())
    )
  }
  
  if (searchForm.value.projectName) {
    data = data.filter(item => 
      item.projectName.toLowerCase().includes(searchForm.value.projectName.toLowerCase())
    )
  }
  
  if (searchForm.value.procurementMethod) {
    data = data.filter(item => 
      item.procurementMethod === searchForm.value.procurementMethod
    )
  }
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return data.slice(start, end)
})

// 供应商筛选后的数据
const filteredSuppliers = computed(() => {
  let data = supplierDatabase.value

  if (supplierFilter.value.region) {
    data = data.filter(supplier => supplier.region === supplierFilter.value.region)
  }

  if (supplierFilter.value.qualification) {
    data = data.filter(supplier => supplier.qualification === supplierFilter.value.qualification)
  }

  if (supplierFilter.value.keyword) {
    data = data.filter(supplier =>
      supplier.name.toLowerCase().includes(supplierFilter.value.keyword.toLowerCase())
    )
  }

  return data
})

// 供应商分页后的数据
const paginatedSuppliers = computed(() => {
  const start = (supplierCurrentPage.value - 1) * supplierPageSize.value
  const end = start + supplierPageSize.value
  return filteredSuppliers.value.slice(start, end)
})

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取采购方式标签类型
const getProcurementMethodType = (method: string) => {
  switch (method) {
    case '公开招标': return 'success'
    case '竞争性谈判': return 'warning'
    case '询价采购': return 'info'
    case '单一来源': return 'danger'
    default: return ''
  }
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已完成': return 'success'
    case '待开标': return 'warning'
    case '进行中': return 'primary'
    case '已取消': return 'danger'
    default: return ''
  }
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  ElMessage.success('查询完成')
}

const handleReset = () => {
  searchForm.value = {
    projectNumber: '',
    projectName: '',
    procurementMethod: ''
  }
  currentPage.value = 1
  ElMessage.info('已重置搜索条件')
}

const handleAdd = () => {
  ElMessage.info('新增项目功能待开发')
}

const handleExport = () => {
  ElMessage.info('导出功能待开发')
}

const handleView = (row: any) => {
  router.push(`/procurement/detail/${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑项目: ${row.projectName}`)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 处理单行导出
const handleRowExport = (row: any) => {
  currentExportProject.value = row

  // 检查该项目的供应商CA绑定状态
  const suppliers = row.suppliers || []
  unboundSuppliers.value = suppliers.filter(supplier => !supplier.caBinding)

  // 显示CA检查弹窗
  caCheckDialogVisible.value = true
}

// 关闭CA检查弹窗
const handleCaCheckDialogClose = () => {
  caCheckDialogVisible.value = false
  currentExportProject.value = null
  unboundSuppliers.value = []
}

// 确认导出
const confirmExport = () => {
  if (!currentExportProject.value) return

  const projectName = currentExportProject.value.projectName
  const unboundCount = unboundSuppliers.value.length

  // 执行实际的导出逻辑
  performExport(currentExportProject.value)

  // 关闭弹窗
  handleCaCheckDialogClose()

  // 显示导出结果消息
  if (unboundCount > 0) {
    ElMessage.warning(`项目"${projectName}"导出成功！注意：有${unboundCount}家供应商未绑定CA，无法参与后续投标、开评标工作。`)
  } else {
    ElMessage.success(`项目"${projectName}"导出成功！所有供应商均已绑定CA。`)
  }
}

// 执行实际的导出操作
const performExport = (project: any) => {
  // 这里实现实际的导出逻辑
  // 例如：生成Excel文件、调用后端API等
  console.log('导出项目:', project)

  // 模拟导出过程
  const exportData = {
    projectInfo: {
      projectNumber: project.projectNumber,
      projectName: project.projectName,
      purchaser: project.purchaser,
      procurementMethod: project.procurementMethod,
      amount: project.amount,
      bidOpeningTime: project.bidOpeningTime,
      status: project.status
    },
    suppliers: project.suppliers || [],
    unboundSuppliers: unboundSuppliers.value,
    exportTime: new Date().toLocaleString()
  }

  // 这里可以调用实际的导出服务
  // exportService.exportProject(exportData)
}

// 供应商选择相关方法
// 打开供应商选择弹窗
const handleSelectSuppliers = (row: any) => {
  currentSelectProject.value = row
  supplierSelectDialogVisible.value = true

  // 重置筛选条件和分页
  supplierFilter.value = {
    region: '',
    qualification: '',
    keyword: ''
  }
  supplierCurrentPage.value = 1
  selectedSuppliers.value = []
}

// 关闭供应商选择弹窗
const handleSupplierSelectDialogClose = () => {
  supplierSelectDialogVisible.value = false
  currentSelectProject.value = null
  selectedSuppliers.value = []
  supplierFilter.value = {
    region: '',
    qualification: '',
    keyword: ''
  }
  supplierCurrentPage.value = 1
}

// 供应商搜索
const handleSupplierSearch = () => {
  supplierCurrentPage.value = 1
  ElMessage.info('已更新搜索结果')
}

// 重置供应商筛选条件
const handleSupplierFilterReset = () => {
  supplierFilter.value = {
    region: '',
    qualification: '',
    keyword: ''
  }
  supplierCurrentPage.value = 1
  ElMessage.info('已重置筛选条件')
}

// 清空选择的供应商
const clearSelectedSuppliers = () => {
  selectedSuppliers.value = []
  // 清空表格选择状态
  if (supplierTableRef.value) {
    supplierTableRef.value.clearSelection()
  }
  ElMessage.info('已清空选择')
}

// 处理供应商选择变化
const handleSupplierSelectionChange = (selection: any[]) => {
  selectedSuppliers.value = selection
}

// 供应商分页相关
const handleSupplierSizeChange = (val: number) => {
  supplierPageSize.value = val
  supplierCurrentPage.value = 1
}

const handleSupplierCurrentChange = (val: number) => {
  supplierCurrentPage.value = val
}

// 进入标项选择步骤
const proceedToTargetSelection = () => {
  if (selectedSuppliers.value.length === 0) return

  // 关闭供应商选择弹窗，打开标项选择弹窗
  supplierSelectDialogVisible.value = false
  targetSelectDialogVisible.value = true
  selectedTargets.value = []
}

// 返回供应商选择
const backToSupplierSelection = () => {
  targetSelectDialogVisible.value = false
  supplierSelectDialogVisible.value = true
  selectedTargets.value = []
}

// 关闭标项选择弹窗
const handleTargetSelectDialogClose = () => {
  targetSelectDialogVisible.value = false
  selectedTargets.value = []
  // 同时关闭供应商选择相关状态
  handleSupplierSelectDialogClose()
}

// 确认最终选择
const confirmFinalSelection = () => {
  if (!currentSelectProject.value || selectedSuppliers.value.length === 0 || selectedTargets.value.length === 0) return

  const projectName = currentSelectProject.value.projectName
  const selectedSupplierCount = selectedSuppliers.value.length
  const selectedTargetCount = selectedTargets.value.length

  // 为选中的供应商添加标项信息
  const suppliersWithTargets = selectedSuppliers.value.map(supplier => ({
    ...supplier,
    participatingTargets: [...selectedTargets.value]
  }))

  // 更新项目的供应商信息
  const projectIndex = tableData.value.findIndex(p => p.id === currentSelectProject.value.id)
  if (projectIndex !== -1) {
    // 保留原有供应商，添加新选择的供应商
    const existingSuppliers = tableData.value[projectIndex].suppliers || []
    tableData.value[projectIndex].suppliers = [...existingSuppliers, ...suppliersWithTargets]
  }

  // 关闭弹窗
  handleTargetSelectDialogClose()

  // 显示成功消息
  const targetNames = projectTargets.value
    .filter(target => selectedTargets.value.includes(target.id))
    .map(target => target.name)
    .join('、')

  ElMessage.success(`已为项目"${projectName}"选择${selectedSupplierCount}家供应商参与${selectedTargetCount}个标项：${targetNames}`)
}
</script>

<style scoped>
.procurement-list {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - var(--header-height) - 60px);
}

.search-section {
  margin-bottom: 16px;
}

.search-form {
  padding: 8px 0;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.table-section {
  background: white;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.table-count {
  font-size: 14px;
  color: var(--text-color-secondary);
  font-weight: normal;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.amount-text {
  color: #ff4d4f;
  font-weight: 600;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

:deep(.el-table th) {
  background-color: #fafafa !important;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-link) {
  font-weight: normal;
}

/* CA检查弹窗样式 */
.ca-check-container {
  padding: 10px 0;
}

.warning-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
  border-radius: 6px;
}

.warning-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.main-message {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
  line-height: 1.4;
}

.sub-message {
  margin: 0;
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

.supplier-list {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.list-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.supplier-count {
  font-size: 12px;
  color: #909399;
}

.no-unbound-suppliers {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #f0f9ff;
  border: 1px solid #d1ecf1;
  border-radius: 6px;
}

.success-icon {
  font-size: 20px;
  margin-right: 8px;
}

.success-message {
  font-size: 14px;
  color: #67c23a;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 供应商选择弹窗样式 */
:deep(.supplier-select-dialog) {
  max-height: 90vh;
}

:deep(.supplier-select-dialog .el-dialog__body) {
  max-height: calc(90vh - 120px);
  overflow-y: auto;
  padding: 20px;
}

.supplier-select-container {
  padding: 0;
  height: calc(90vh - 160px);
  display: flex;
  flex-direction: column;
}

.filter-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.filter-form {
  padding: 12px 16px;
}

.filter-buttons {
  display: flex;
  gap: 12px;
}

.supplier-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.supplier-list-section .el-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.supplier-list-section .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.list-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.list-count {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-count {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  flex-shrink: 0;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

/* 标项选择弹窗样式 */
.target-select-container {
  padding: 20px 0;
}

.selected-suppliers-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f9ff;
  border: 1px solid #d1ecf1;
  border-radius: 6px;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-icon {
  font-size: 18px;
  margin-right: 8px;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.supplier-count {
  font-size: 12px;
  color: #409eff;
  background-color: #ecf5ff;
  padding: 2px 8px;
  border-radius: 12px;
}

.suppliers-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.supplier-tag {
  margin: 0;
}

.target-selection-section {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.targets-list {
  max-height: 300px;
  overflow-y: auto;
}

.target-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: all 0.3s;
}

.target-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.target-checkbox {
  width: 100%;
}

.target-content {
  margin-left: 24px;
}

.target-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.target-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

:deep(.target-checkbox .el-checkbox__label) {
  width: 100%;
  padding-left: 0;
}


</style>
