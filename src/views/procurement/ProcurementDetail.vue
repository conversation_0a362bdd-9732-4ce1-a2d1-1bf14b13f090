<template>
  <div class="procurement-detail">
    <!-- 自定义顶部导航栏 -->
    <div class="custom-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">
            <el-icon><House /></el-icon>
          </div>
          <span class="system-name">政府采购管理平台</span>
        </div>
      </div>
      <div class="header-right">
        <el-button type="text" class="notification-btn">
          <el-icon><Bell /></el-icon>
        </el-button>
        <div class="user-info">
          <el-icon><User /></el-icon>
          <span class="username">管理员</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
      </div>
    </div>



    <!-- 项目标题栏 -->
    <div class="project-header">
      <div class="project-title">
        <el-button type="primary" size="small" class="back-btn" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <span class="title-text">新列装综合台测试项目</span>
        <span class="project-code">[5678-20250770002]</span>
      </div>
      <div class="header-actions">
        <el-dropdown>
          <el-button type="primary">
            项目管理操作 (0)
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>操作1</el-dropdown-item>
              <el-dropdown-item>操作2</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown>
          <el-button>
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>导出</el-dropdown-item>
              <el-dropdown-item>打印</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="detail-content">
      <!-- 左侧导航 -->
      <div class="left-nav">
        <div class="nav-item active">
          <el-icon><Document /></el-icon>
          <span>项目信息</span>
        </div>
        <div class="nav-item">
          <el-icon><Edit /></el-icon>
          <span>变更公告</span>
          <span class="nav-date">2025-07-16</span>
        </div>
        <div class="nav-item">
          <el-icon><Warning /></el-icon>
          <span>澄清公告发布</span>
        </div>
        <div class="nav-item">
          <el-icon><Files /></el-icon>
          <span>开标信息</span>
        </div>
        <div class="nav-item">
          <el-icon><User /></el-icon>
          <span>法规信息发布</span>
        </div>
        <div class="nav-item">
          <el-icon><Setting /></el-icon>
          <span>合同信息</span>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 项目基本信息 -->
        <div class="info-section">
          <h3 class="section-title">项目基本信息</h3>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>项目名称</label>
                <span>新列装综合台测试项目</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>项目编号</label>
                <span>5678-20250770002</span>
              </div>
              <div class="info-item">
                <label>项目类型</label>
                <span>项目类型</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>组织形式</label>
                <span>政府集中采购</span>
              </div>
              <div class="info-item">
                <label>采购方式</label>
                <span>竞争性谈判</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>项目区域</label>
                <span>吉林市本级</span>
              </div>
              <div class="info-item">
                <label>采购单位</label>
                <span>吉林市水务集团有限公司</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>项目金额（元）</label>
                <span class="amount">1,000.00</span>
              </div>
              <div class="info-item">
                <label>项目负责人</label>
                <span>吉林某某</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>项目联系人及联系方式</label>
                <span>18210000012</span>
              </div>
              <div class="info-item">
                <label>项目开标方式</label>
                <span>电子标</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 采购公告 -->
        <div class="info-section">
          <h3 class="section-title">采购公告</h3>
          <div class="notice-content">
            <p>关于采购新综合台的公告</p>
            <el-button type="primary" size="small" class="view-btn">点击查看</el-button>
          </div>
        </div>

        <!-- 采购文件管理 -->
        <div class="info-section">
          <h3 class="section-title">采购文件管理</h3>
          <!-- 文件步骤区域 -->
          <div class="file-steps-container">
            <div class="file-step-row">
              <!-- 第一步 -->
              <div class="file-step-item">
                <div class="step-header">
                  <span class="step-marker">★</span>
                  <span class="step-title">第一步：上传采购文件</span>
                </div>
                <div class="step-content">
                  <p class="step-desc">请上传采购文件</p>
                  <el-button type="primary" size="small" class="step-button">点击设置</el-button>
                </div>
              </div>

              <!-- 第二步 -->
              <div class="file-step-item">
                <div class="step-header">
                  <span class="step-marker">★</span>
                  <span class="step-title">第二步：设定采购文件</span>
                </div>
                <div class="step-content">
                  <p class="step-desc">设定完成进度：3/6</p>
                  <el-button type="primary" size="small" class="step-button" @click="openFileConfig">点击设置</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 相关附件 -->
          <div class="attachment-section">
            <h4>相关附件</h4>
            <el-table :data="attachments" style="width: 100%" class="attachment-table">
              <el-table-column prop="type" label="文件类型" width="120" />
              <el-table-column prop="name" label="文件名称" />
              <el-table-column prop="uploadTime" label="文件上传时间" width="180" />
              <el-table-column label="操作" width="100">
                <template #default>
                  <el-button type="text" size="small">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 最新文件 -->
        <div class="info-section">
          <div class="latest-file">
            <span>最新文件</span>
            <span>请从上传品牌的资质证明文件</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  ArrowDown,
  Document,
  Edit,
  Warning,
  Files,
  User,
  Setting,
  House,
  Bell
} from '@element-plus/icons-vue'

const router = useRouter()

// 附件数据
const attachments = ref([
  {
    type: '资格文件',
    name: '请从上传品牌的资质证明文件',
    uploadTime: '-'
  }
])

// 返回列表
const goBack = () => {
  router.push('/procurement/list')
}

// 打开文件配置页面
const openFileConfig = () => {
  router.push('/procurement/file-config/1')
}
</script>

<style scoped>
.procurement-detail {
  background-color: #f0f2f5;
  min-height: 100vh;
  width: 100%;
}

/* 自定义顶部导航栏 */
.custom-header {
  height: 60px;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.system-name {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-btn {
  padding: 8px;
  color: rgba(255, 255, 255, 0.85) !important;
}

.notification-btn:hover {
  color: white !important;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.username {
  font-size: 14px;
  color: white;
  margin-left: 4px;
  margin-right: 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
}



.project-header {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.title-text {
  font-size: 16px;
  font-weight: 600;
}

.project-code {
  font-size: 14px;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.detail-content {
  display: flex;
  padding: 20px;
  gap: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 108px);
}

.left-nav {
  width: 200px;
  background: white;
  border-radius: 4px;
  padding: 0;
  height: fit-content;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.nav-item:hover {
  background-color: #f5f7fa;
}

.nav-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-right: 3px solid #1890ff;
}

.nav-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.nav-date {
  margin-left: auto;
  font-size: 12px;
  color: #8c8c8c;
}

.right-content {
  flex: 1;
}

.info-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  gap: 40px;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.info-item label {
  width: 140px;
  color: #8c8c8c;
  font-size: 14px;
}

.info-item span {
  color: #262626;
  font-size: 14px;
}

.amount {
  color: #ff4d4f;
  font-weight: 600;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-btn {
  padding: 4px 12px;
}

/* 文件管理选项卡 */
.file-management-tabs {
  margin-bottom: 20px;
}

.custom-tabs {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0;
}

.tab-content-wrapper {
  padding: 16px 20px;
  background: white;
  border-radius: 0 0 4px 4px;
}

.method-options {
  margin-bottom: 16px;
}

.method-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.method-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 文件步骤容器 */
.file-steps-container {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.file-step-row {
  display: flex;
  gap: 40px;
}

.file-step-item {
  flex: 1;
  background: white;
  border-radius: 4px;
  padding: 16px;
  border: 1px solid #e8e8e8;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.step-marker {
  color: #ff4d4f;
  font-weight: 600;
  margin-right: 8px;
  font-size: 14px;
}

.step-title {
  color: #262626;
  font-weight: 600;
  font-size: 14px;
}

.step-content {
  padding-left: 22px;
}

.step-desc {
  color: #8c8c8c;
  margin-bottom: 12px;
  font-size: 13px;
}

.step-button {
  padding: 4px 12px;
  font-size: 12px;
}

.attachment-section h4 {
  margin: 20px 0 12px 0;
  color: #262626;
}

.latest-file {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #8c8c8c;
}

/* 选项卡样式 */
:deep(.custom-tabs .el-tabs__header) {
  margin-bottom: 0;
  background: #f8f9fa;
  border-radius: 4px 4px 0 0;
  padding: 0 20px;
}

:deep(.custom-tabs .el-tabs__nav-wrap::after) {
  height: 0;
}

:deep(.custom-tabs .el-tabs__nav) {
  border: none;
}

:deep(.custom-tabs .el-tabs__item) {
  border: none;
  color: #666;
  font-size: 14px;
  padding: 0 16px;
  height: 40px;
  line-height: 40px;
}

:deep(.custom-tabs .el-tabs__item.is-active) {
  color: #1890ff;
  background: white;
  border-radius: 4px 4px 0 0;
  border-bottom: 2px solid #1890ff;
}

:deep(.custom-tabs .el-tabs__active-bar) {
  display: none;
}

:deep(.custom-tabs .el-tabs__content) {
  padding: 0;
}
</style>
