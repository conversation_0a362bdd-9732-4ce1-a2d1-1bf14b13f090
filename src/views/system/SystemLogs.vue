<template>
  <div class="system-logs">
    <div class="page-header">
      <h2>系统日志</h2>
      <p>查看系统运行日志</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="filter-bar">
          <el-select v-model="logLevel" placeholder="选择日志级别" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="margin-left: 16px"
          />
          
          <el-button type="primary" style="margin-left: 16px" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </div>
        
        <el-table :data="logList" style="width: 100%; margin-top: 16px">
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column prop="level" label="级别" width="80">
            <template #default="scope">
              <el-tag 
                :type="getLogLevelType(scope.row.level)"
                size="small"
              >
                {{ scope.row.level }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="module" label="模块" width="120" />
          <el-table-column prop="message" label="消息" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const logLevel = ref('')
const dateRange = ref('')

const logList = ref([
  { time: '2024-01-15 10:30:25', level: '信息', module: '用户管理', message: '用户登录成功' },
  { time: '2024-01-15 10:25:12', level: '警告', module: '系统', message: '内存使用率超过80%' },
  { time: '2024-01-15 10:20:08', level: '错误', module: '数据库', message: '连接超时' },
  { time: '2024-01-15 10:15:33', level: '信息', module: '权限', message: '角色权限更新' }
])

const getLogLevelType = (level: string) => {
  switch (level) {
    case '信息': return 'success'
    case '警告': return 'warning'
    case '错误': return 'danger'
    default: return ''
  }
}

const handleSearch = () => {
  ElMessage.info('查询日志功能待开发')
}
</script>

<style scoped>
.system-logs {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.filter-bar {
  display: flex;
  align-items: center;
}
</style>
