<template>
  <div class="system-config">
    <div class="page-header">
      <h2>系统配置</h2>
      <p>管理系统基础配置信息</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <el-form :model="configForm" label-width="120px">
          <el-form-item label="系统名称">
            <el-input v-model="configForm.systemName" />
          </el-form-item>
          
          <el-form-item label="系统版本">
            <el-input v-model="configForm.version" />
          </el-form-item>
          
          <el-form-item label="维护模式">
            <el-switch v-model="configForm.maintenanceMode" />
          </el-form-item>
          
          <el-form-item label="允许注册">
            <el-switch v-model="configForm.allowRegistration" />
          </el-form-item>
          
          <el-form-item label="系统描述">
            <el-input 
              v-model="configForm.description" 
              type="textarea" 
              :rows="4"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSave">保存配置</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const configForm = ref({
  systemName: '通用管理系统',
  version: '1.0.0',
  maintenanceMode: false,
  allowRegistration: true,
  description: '这是一个基于 Vue 3 + TypeScript + Element Plus 的通用管理系统框架'
})

const handleSave = () => {
  ElMessage.success('配置保存成功')
}

const handleReset = () => {
  configForm.value = {
    systemName: '通用管理系统',
    version: '1.0.0',
    maintenanceMode: false,
    allowRegistration: true,
    description: '这是一个基于 Vue 3 + TypeScript + Element Plus 的通用管理系统框架'
  }
  ElMessage.info('配置已重置')
}
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}
</style>
