<template>
  <div class="about-view">
    <div class="page-header">
      <h2>关于系统</h2>
      <p>系统信息和版本详情</p>
    </div>

    <div class="content-area">
      <el-card>
        <div class="about-content">
          <div class="system-info">
            <h3>通用管理系统</h3>
            <p class="version">版本 1.0.0</p>
            <p class="description">
              这是一个基于 Vue 3 + TypeScript + Element Plus 构建的通用管理系统框架，
              提供了完整的后台管理功能模块，包括用户管理、系统配置、数据分析等功能。
            </p>
          </div>

          <div class="tech-stack">
            <h4>技术栈</h4>
            <div class="tech-tags">
              <el-tag>Vue 3</el-tag>
              <el-tag>TypeScript</el-tag>
              <el-tag>Element Plus</el-tag>
              <el-tag>Vite</el-tag>
              <el-tag>Pinia</el-tag>
              <el-tag>Vue Router</el-tag>
            </div>
          </div>

          <div class="features">
            <h4>主要特性</h4>
            <ul>
              <li>响应式设计，支持多种屏幕尺寸</li>
              <li>模块化架构，易于扩展和维护</li>
              <li>完整的权限管理系统</li>
              <li>丰富的组件库和工具函数</li>
              <li>TypeScript 支持，提供更好的开发体验</li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.about-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.about-content {
  max-width: 800px;
}

.system-info h3 {
  color: var(--text-color);
  margin-bottom: 8px;
}

.version {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 16px;
}

.description {
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin-bottom: 32px;
}

.tech-stack,
.features {
  margin-bottom: 32px;
}

.tech-stack h4,
.features h4 {
  color: var(--text-color);
  margin-bottom: 16px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.features ul {
  color: var(--text-color-secondary);
  line-height: 1.8;
  padding-left: 20px;
}
</style>

<style>
@media (min-width: 1024px) {
  .about {
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
}
</style>
