<template>
  <div class="home-view">
    <div class="welcome-content">
      <h1>欢迎使用政府采购管理平台</h1>
      <p>统一管理政府采购项目，提高采购效率，确保采购透明</p>

      <div class="feature-cards">
        <el-card class="feature-card" shadow="hover" @click="$router.push('/procurement/list')">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>采购项目管理</span>
            </div>
          </template>
          <p>管理政府采购项目，包括项目发布、招标、评标等全流程</p>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>供应商管理</span>
            </div>
          </template>
          <p>供应商注册、资质审核、信用评价等管理功能</p>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>统计分析</span>
            </div>
          </template>
          <p>采购数据统计分析，生成各类采购报表</p>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Document, DataAnalysis } from '@element-plus/icons-vue'
</script>

<style scoped>
.home-view {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - var(--header-height) - 60px);
}

.welcome-content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-content h1 {
  font-size: 32px;
  color: var(--text-color);
  margin-bottom: 16px;
}

.welcome-content p {
  font-size: 16px;
  color: var(--text-color-secondary);
  margin-bottom: 40px;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.feature-card {
  text-align: left;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-color);
}

.card-header .el-icon {
  font-size: 18px;
  color: var(--primary-color);
}
</style>
