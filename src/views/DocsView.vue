<template>
  <div class="docs-view">
    <div class="page-header">
      <h2>文档中心</h2>
      <p>系统使用文档和帮助信息</p>
    </div>
    
    <div class="content-area">
      <div class="docs-layout">
        <div class="docs-sidebar">
          <el-menu
            :default-active="activeDoc"
            @select="handleDocSelect"
            class="docs-menu"
          >
            <el-menu-item index="getting-started">
              <el-icon><Document /></el-icon>
              <span>快速开始</span>
            </el-menu-item>
            <el-menu-item index="user-guide">
              <el-icon><Reading /></el-icon>
              <span>用户指南</span>
            </el-menu-item>
            <el-menu-item index="api-docs">
              <el-icon><Connection /></el-icon>
              <span>API 文档</span>
            </el-menu-item>
            <el-menu-item index="faq">
              <el-icon><QuestionFilled /></el-icon>
              <span>常见问题</span>
            </el-menu-item>
          </el-menu>
        </div>
        
        <div class="docs-content">
          <el-card>
            <div v-if="activeDoc === 'getting-started'" class="doc-section">
              <h3>快速开始</h3>
              <p>欢迎使用通用管理系统！本文档将帮助您快速上手。</p>
              
              <h4>系统要求</h4>
              <ul>
                <li>Node.js 16.0 或更高版本</li>
                <li>现代浏览器（Chrome、Firefox、Safari、Edge）</li>
              </ul>
              
              <h4>安装步骤</h4>
              <ol>
                <li>克隆项目代码</li>
                <li>安装依赖：<code>npm install</code></li>
                <li>启动开发服务器：<code>npm run dev</code></li>
              </ol>
            </div>
            
            <div v-else-if="activeDoc === 'user-guide'" class="doc-section">
              <h3>用户指南</h3>
              <p>详细的功能使用说明。</p>
              
              <h4>用户管理</h4>
              <p>在用户管理模块中，您可以：</p>
              <ul>
                <li>查看用户列表</li>
                <li>添加新用户</li>
                <li>编辑用户信息</li>
                <li>管理用户角色</li>
              </ul>
            </div>
            
            <div v-else-if="activeDoc === 'api-docs'" class="doc-section">
              <h3>API 文档</h3>
              <p>系统 API 接口说明。</p>
              
              <h4>认证接口</h4>
              <p><strong>POST</strong> /api/auth/login</p>
              <p>用户登录接口</p>
              
              <h4>用户接口</h4>
              <p><strong>GET</strong> /api/users</p>
              <p>获取用户列表</p>
            </div>
            
            <div v-else-if="activeDoc === 'faq'" class="doc-section">
              <h3>常见问题</h3>
              
              <h4>Q: 如何重置密码？</h4>
              <p>A: 请联系系统管理员重置密码。</p>
              
              <h4>Q: 忘记用户名怎么办？</h4>
              <p>A: 请使用注册邮箱联系管理员。</p>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Document, Reading, Connection, QuestionFilled } from '@element-plus/icons-vue'

const activeDoc = ref('getting-started')

const handleDocSelect = (key: string) => {
  activeDoc.value = key
}
</script>

<style scoped>
.docs-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.docs-layout {
  display: flex;
  gap: 24px;
  height: calc(100vh - 200px);
}

.docs-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.docs-menu {
  border-right: none;
  height: 100%;
}

.docs-content {
  flex: 1;
  overflow-y: auto;
}

.doc-section h3 {
  color: var(--text-color);
  margin-bottom: 16px;
}

.doc-section h4 {
  color: var(--text-color);
  margin: 20px 0 12px 0;
}

.doc-section p {
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin-bottom: 12px;
}

.doc-section ul,
.doc-section ol {
  color: var(--text-color-secondary);
  line-height: 1.6;
  margin-bottom: 12px;
  padding-left: 20px;
}

.doc-section code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: var(--primary-color);
}
</style>
