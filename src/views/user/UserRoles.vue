<template>
  <div class="user-roles">
    <div class="page-header">
      <h2>角色管理</h2>
      <p>管理系统角色和权限</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="table-header">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
        
        <el-table :data="roleList" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="角色名称" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="permissions" label="权限数量" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const roleList = ref([
  { id: 1, name: '超级管理员', description: '拥有所有权限', permissions: 50 },
  { id: 2, name: '管理员', description: '拥有大部分权限', permissions: 30 },
  { id: 3, name: '普通用户', description: '基础权限', permissions: 10 }
])

const handleAdd = () => {
  ElMessage.info('新增角色功能待开发')
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑角色: ${row.name}`)
}

const handleDelete = (row: any) => {
  ElMessage.info(`删除角色: ${row.name}`)
}
</script>

<style scoped>
.user-roles {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.table-header {
  margin-bottom: 16px;
}
</style>
