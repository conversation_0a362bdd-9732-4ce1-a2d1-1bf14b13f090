<template>
  <div class="user-list">
    <div class="page-header">
      <h2>用户列表</h2>
      <p>管理系统用户信息</p>
    </div>
    
    <div class="content-area">
      <el-card>
        <div class="table-header">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
        
        <el-table :data="userList" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="用户名" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="role" label="角色" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const userList = ref([
  { id: 1, name: '张三', email: '<EMAIL>', role: '管理员', status: '正常' },
  { id: 2, name: '李四', email: '<EMAIL>', role: '用户', status: '正常' },
  { id: 3, name: '王五', email: '<EMAIL>', role: '用户', status: '禁用' }
])

const handleAdd = () => {
  ElMessage.info('新增用户功能待开发')
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑用户: ${row.name}`)
}

const handleDelete = (row: any) => {
  ElMessage.info(`删除用户: ${row.name}`)
}
</script>

<style scoped>
.user-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.page-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.table-header {
  margin-bottom: 16px;
}
</style>
