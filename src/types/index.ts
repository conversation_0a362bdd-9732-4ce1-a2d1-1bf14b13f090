// 用户信息类型
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
}

// 认证状态类型
export interface AuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
}

// 菜单项类型
export interface MenuItem {
  id: string
  title: string
  icon?: any
  path?: string
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    requiresLayout?: boolean
  }
}

// 面包屑项类型
export interface BreadcrumbItem {
  title: string
  path?: string
  disabled?: boolean
}
