<template>
  <aside class="app-sidebar">
    <!-- 折叠按钮 -->
    <div class="sidebar-header">
      <el-button 
        type="text" 
        @click="toggleCollapse"
        class="collapse-btn"
      >
        <el-icon>
          <Fold v-if="!isCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapsed"
      :router="true"
      class="sidebar-menu"
      background-color="#ffffff"
      text-color="#303133"
      active-text-color="#409eff"
    >
      <template v-for="item in menuList" :key="item.id">
        <!-- 有子菜单的项 -->
        <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
          <template #title>
            <el-icon v-if="item.icon">
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.title }}</span>
          </template>
          <el-menu-item 
            v-for="child in item.children" 
            :key="child.id"
            :index="child.path"
          >
            {{ child.title }}
          </el-menu-item>
        </el-sub-menu>

        <!-- 无子菜单的项 -->
        <el-menu-item v-else :index="item.path">
          <el-icon v-if="item.icon">
            <component :is="item.icon" />
          </el-icon>
          <template #title>{{ item.title }}</template>
        </el-menu-item>
      </template>
    </el-menu>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import type { MenuItem } from '@/types'
import { 
  Fold, 
  Expand, 
  House, 
  User, 
  Setting, 
  Document,
  DataAnalysis,
  Management
} from '@element-plus/icons-vue'

const route = useRoute()
const isCollapsed = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 菜单数据
const menuList: MenuItem[] = [
  {
    id: 'home',
    title: '首页',
    icon: House,
    path: '/'
  },
  {
    id: 'procurement',
    title: '采购管理',
    icon: Document,
    children: [
      {
        id: 'procurement-list',
        title: '采购项目列表',
        path: '/procurement/list'
      },
      {
        id: 'procurement-plan',
        title: '采购计划',
        path: '/procurement/plan'
      },
      {
        id: 'procurement-contract',
        title: '合同管理',
        path: '/procurement/contract'
      }
    ]
  },
  {
    id: 'user',
    title: '用户管理',
    icon: User,
    children: [
      {
        id: 'user-list',
        title: '用户列表',
        path: '/user/list'
      },
      {
        id: 'user-roles',
        title: '角色管理',
        path: '/user/roles'
      }
    ]
  },
  {
    id: 'system',
    title: '系统管理',
    icon: Setting,
    children: [
      {
        id: 'system-config',
        title: '系统配置',
        path: '/system/config'
      },
      {
        id: 'system-logs',
        title: '系统日志',
        path: '/system/logs'
      }
    ]
  },
  {
    id: 'reports',
    title: '报表分析',
    icon: DataAnalysis,
    path: '/reports'
  },
  {
    id: 'docs',
    title: '文档中心',
    icon: Document,
    path: '/docs'
  }
]
</script>

<style scoped>
.app-sidebar {
  background-color: #ffffff;
  border-right: 1px solid #e4e7ed;
}

.sidebar-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
}

.collapse-btn {
  padding: 8px;
  color: #606266;
}

.collapse-btn:hover {
  color: #409eff;
}

.sidebar-menu {
  border-right: none;
  height: calc(100% - 50px);
}

:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  border-right: 2px solid #409eff;
}

:deep(.el-menu--collapse .el-menu-item span),
:deep(.el-menu--collapse .el-sub-menu__title span) {
  display: none;
}

:deep(.el-menu--collapse .el-menu-item .el-icon),
:deep(.el-menu--collapse .el-sub-menu__title .el-icon) {
  margin: 0;
  vertical-align: middle;
  width: 24px;
  text-align: center;
}

:deep(.el-menu--collapse .el-sub-menu) {
  position: relative;
}

:deep(.el-menu--collapse .el-sub-menu .el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: none;
}
</style>
