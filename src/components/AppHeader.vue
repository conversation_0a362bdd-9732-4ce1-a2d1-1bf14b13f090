<template>
  <header class="app-header">
    <div class="header-left">
      <!-- Logo -->
      <div class="logo">
        <div class="logo-icon">
          <el-icon size="24" color="white"><Setting /></el-icon>
        </div>
        <span class="system-name">政府采购管理平台</span>
      </div>

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <el-button type="text" class="nav-item">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-button>
        <el-button type="text" class="nav-item">
          <el-icon><Document /></el-icon>
          <span>项目管理</span>
        </el-button>
        <el-button type="text" class="nav-item">
          <el-icon><DataAnalysis /></el-icon>
          <span>统计分析</span>
        </el-button>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 通知图标 -->
      <el-button type="text" class="notification-btn">
        <el-icon size="18"><Bell /></el-icon>
      </el-button>
      
      <!-- 用户信息 -->
      <el-dropdown @command="handleCommand" trigger="click" class="user-dropdown">
        <div class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userName }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

import { Bell, User, ArrowDown, SwitchButton, Setting, House, Document, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const userName = computed(() => authStore.currentUser?.name || '用户')
const userAvatar = computed(() => authStore.currentUser?.avatar)

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人信息页面
      console.log('跳转到个人信息页面')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.logo {
  display: flex;
  align-items: center;
  margin-right: 24px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.system-name {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.nav-menu {
  display: flex;
  align-items: center;
  margin-left: 40px;
  gap: 8px;
}

.nav-item {
  color: rgba(255, 255, 255, 0.85) !important;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.2s;
}

.nav-item:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item span {
  margin-left: 4px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-btn {
  padding: 8px;
  color: rgba(255, 255, 255, 0.85) !important;
}

.notification-btn:hover {
  color: white !important;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.username {
  font-size: 14px;
  color: white;
  margin-left: 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  transition: transform 0.2s;
}

.user-dropdown.is-opened .dropdown-icon {
  transform: rotate(180deg);
}
</style>
