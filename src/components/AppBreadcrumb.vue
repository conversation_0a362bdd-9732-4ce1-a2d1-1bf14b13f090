<template>
  <div class="breadcrumb-container">
    <div class="breadcrumb-wrapper">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :key="index"
          :to="item.path && index < breadcrumbList.length - 1 ? item.path : undefined"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import type { BreadcrumbItem } from '@/types'

const route = useRoute()

// 根据路由生成面包屑
const breadcrumbList = computed((): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = []

  // 添加首页
  if (route.path !== '/') {
    breadcrumbs.push({
      title: '首页',
      path: '/'
    })
  }

  // 特殊处理详情页面
  if (route.name === 'procurement-detail') {
    breadcrumbs.push({
      title: '采购管理',
      path: undefined
    })
    breadcrumbs.push({
      title: '采购项目列表',
      path: '/procurement/list'
    })
    breadcrumbs.push({
      title: '项目详情',
      path: undefined
    })
  } else {
    // 其他页面使用默认逻辑
    const matched = route.matched.filter(item => item.meta && item.meta.title)
    matched.forEach((item, index) => {
      const isLast = index === matched.length - 1
      breadcrumbs.push({
        title: item.meta?.title as string,
        path: isLast ? undefined : item.path,
        disabled: isLast
      })
    })
  }

  return breadcrumbs
})
</script>

<style scoped>
.breadcrumb-container {
  background-color: white;
  border-bottom: 1px solid var(--border-color-light);
  margin-bottom: 16px;
}

.breadcrumb-wrapper {
  padding: 12px 20px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  font-weight: 600;
  color: var(--text-color);
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  color: var(--text-color-secondary);
  font-size: 14px;
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner:hover) {
  color: var(--primary-color);
}

:deep(.el-breadcrumb__separator) {
  color: var(--text-color-secondary);
  margin: 0 8px;
}
</style>
