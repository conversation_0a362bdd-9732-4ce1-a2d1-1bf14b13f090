import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '首页',
        requiresLayout: true
      }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: {
        title: '关于',
        requiresLayout: true
      }
    },
    {
      path: '/procurement/list',
      name: 'procurement-list',
      component: () => import('../views/procurement/ProcurementList.vue'),
      meta: {
        title: '采购项目列表',
        requiresLayout: true
      }
    },
    {
      path: '/procurement/detail/:id',
      name: 'procurement-detail',
      component: () => import('../views/procurement/ProcurementDetail.vue'),
      meta: {
        title: '项目详情',
        requiresLayout: false
      }
    },
    {
      path: '/procurement/file-config/:id',
      name: 'procurement-file-config',
      component: () => import('../views/procurement/ProcurementFileConfig.vue'),
      meta: {
        title: '采购文件设定',
        requiresLayout: false
      }
    },
    {
      path: '/user/list',
      name: 'user-list',
      component: () => import('../views/user/UserList.vue'),
      meta: {
        title: '用户列表',
        requiresLayout: true
      }
    },
    {
      path: '/user/roles',
      name: 'user-roles',
      component: () => import('../views/user/UserRoles.vue'),
      meta: {
        title: '角色管理',
        requiresLayout: true
      }
    },
    {
      path: '/system/config',
      name: 'system-config',
      component: () => import('../views/system/SystemConfig.vue'),
      meta: {
        title: '系统配置',
        requiresLayout: true
      }
    },
    {
      path: '/system/logs',
      name: 'system-logs',
      component: () => import('../views/system/SystemLogs.vue'),
      meta: {
        title: '系统日志',
        requiresLayout: true
      }
    },
    {
      path: '/reports',
      name: 'reports',
      component: () => import('../views/ReportsView.vue'),
      meta: {
        title: '报表分析',
        requiresLayout: true
      }
    },
    {
      path: '/docs',
      name: 'docs',
      component: () => import('../views/DocsView.vue'),
      meta: {
        title: '文档中心',
        requiresLayout: true
      }
    }
  ]
})

export default router
