<template>
  <div class="app-container">
    <AppHeader v-if="showLayout" />
    <AppSidebar v-if="showLayout && showSidebar" />
    <main :class="{'main-content': showLayout, 'main-content-full': showLayout && !showSidebar}">
      <AppBreadcrumb v-if="showLayout" />
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppSidebar from '@/components/AppSidebar.vue'
import AppBreadcrumb from '@/components/AppBreadcrumb.vue'

const route = useRoute()
const showLayout = computed(() => route.meta.requiresLayout !== false)
const showSidebar = computed(() => route.meta.hideSidebar !== true)
</script>

<style scoped>
.app-container {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  overflow-x: hidden;
  max-width: calc(100vw - var(--sidebar-width));
  box-sizing: border-box;
  background-color: #f0f2f5;
  min-height: calc(100vh - var(--header-height));
  padding: 0;
}

.main-content-full {
  max-width: 100vw;
  margin-left: 0 !important;
}
</style>
