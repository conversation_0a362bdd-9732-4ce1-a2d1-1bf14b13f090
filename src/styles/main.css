/* CSS 变量定义 */
:root {
  --header-height: 60px;
  --sidebar-width: 220px;
  --content-padding: 20px;
  --gap-sm: 8px;
  --gap-md: 16px;
  --gap-lg: 24px;
  --border-radius: 4px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --primary-color: #1890ff;
  --primary-dark: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --text-color-light: #bfbfbf;
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  --bg-color: #ffffff;
  --bg-color-light: #fafafa;
  --header-bg: #1890ff;
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 应用容器布局 */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部标题栏 */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background: linear-gradient(90deg, var(--header-bg) 0%, #40a9ff 100%);
  border-bottom: none;
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 0 var(--content-padding);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 左侧导航栏 */
.app-sidebar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  width: var(--sidebar-width);
  height: calc(100vh - var(--header-height));
  background-color: var(--bg-color);
  border-right: 1px solid var(--border-color);
  z-index: 90;
  overflow-y: auto;
}

/* 主内容区 */
.main-content {
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  background-color: var(--bg-color-light);
  max-width: calc(100vw - var(--sidebar-width));
  box-sizing: border-box;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ml-auto {
  margin-left: auto;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
}
