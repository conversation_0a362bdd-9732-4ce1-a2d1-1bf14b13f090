# 标的3引用需求标准功能说明

## 功能概述

参照标的1的技术要求中的"引用需求标准"功能，为标的3的服务要求添加了引用需求标准的操作，支持从服务类政府采购需求标准库中引用标准化的服务要求。

## 功能实现

### 1. 界面添加
在标的3的服务要求编辑抽屉中添加了"引用需求标准"按钮：

```vue
<el-button
  v-if="currentEditingTarget === 'target3'"
  type="warning"
  plain
  size="small"
  @click="referenceServiceStandards"
>
  <el-icon><Document /></el-icon>
  引用需求标准
</el-button>
```

### 2. 服务类标准数据
在需求标准数据中添加了服务类标准：

```javascript
service: [
  {
    id: 'srv_001',
    indicatorCategory: '服务质量',
    primaryIndicator: '保洁服务标准',
    secondaryIndicator: '清洁频次要求',
    requirement: '办公区域每日清洁不少于2次，垃圾清理及时，保持环境整洁卫生',
    usageDescription: '确保办公环境清洁卫生，符合物业管理服务标准',
    otherAttributes: ['必须符合卫生标准', '可以作为评分因素']
  },
  // ... 更多服务标准
]
```

### 3. 采购目录扩展
在采购目录中添加了服务类目录：

```javascript
{
  id: 'service',
  name: '服务类政府采购需求标准',
  year: '(2023年版)',
  code: 'C010101服务类',
  count: 6
}
```

## 服务标准内容

### 已配置的服务标准

1. **保洁服务标准**
   - 分类：服务质量
   - 要求：办公区域每日清洁不少于2次，垃圾清理及时，保持环境整洁卫生
   - 用途：确保办公环境清洁卫生，符合物业管理服务标准

2. **人员资质要求**
   - 分类：服务人员
   - 要求：物业管理人员应持有物业管理师证书或相关专业资格证书
   - 用途：确保服务人员具备专业能力

3. **值守时间安排**
   - 分类：服务时间
   - 要求：提供7×24小时值守服务，工作日8:00-18:00为正常服务时间
   - 用途：确保服务时间覆盖全天候需求

4. **响应时间标准**
   - 分类：应急响应
   - 要求：一般问题2小时内响应，紧急问题30分钟内到达现场
   - 用途：确保及时响应和处理各类问题

5. **服务质量管理**
   - 分类：服务标准
   - 要求：具备ISO9001质量管理体系认证，建立完善的服务质量控制体系
   - 用途：确保服务质量的标准化和规范化

6. **清洁设备配置**
   - 分类：设备要求
   - 要求：配备专业清洁设备，包括吸尘器、洗地机、高压清洗机等
   - 用途：确保清洁作业的专业性和效率

## 操作流程

### 1. 引用标准流程
```
编辑服务要求 → 点击"引用需求标准" → 选择服务类标准 → 选择具体标准 → 点击引用 → 自动添加到服务要求列表
```

### 2. 标准应用逻辑
- 当前编辑标的为target3时，引用的标准会自动添加到服务要求列表
- 引用的标准会转换为服务要求格式：
  - category: 'standard'（引用标准分类）
  - serviceName: 组合一级和二级指标名称
  - requirement: 使用标准的具体要求
  - isReviewFactor: false（默认不作为评审因素）
  - isSubstantive: false（默认不作为实质性响应）

### 3. 分类标签显示
为服务类分类添加了标签颜色映射：
- 服务质量：primary（蓝色）
- 服务人员：success（绿色）
- 服务时间：info（灰色）
- 应急响应：warning（橙色）
- 服务标准：primary（蓝色）
- 设备要求：info（灰色）

## 技术实现细节

### 1. 引用服务标准方法
```javascript
const referenceServiceStandards = () => {
  referenceStandardDialogVisible.value = true
  // 默认选择服务类标准
  selectedCategoryId.value = 'service'
}
```

### 2. 标准应用方法修改
```javascript
const applySelectedStandards = () => {
  if (currentEditingTarget.value === 'target3') {
    // 将选择的标准添加到服务要求列表中
    selectedStandards.value.forEach(standard => {
      const newId = Math.max(...serviceList.value.map(item => item.id)) + 1
      serviceList.value.push({
        id: newId,
        category: 'standard',
        serviceName: `${standard.primaryIndicator}-${standard.secondaryIndicator}`,
        requirement: standard.requirement,
        isReviewFactor: false,
        isSubstantive: false
      })
    })
    ElMessage.success(`成功引用 ${selectedStandards.value.length} 条服务标准`)
  } else {
    // 原有的技术要求引用逻辑
  }
}
```

### 3. 服务分类扩展
在服务要求的分类选择中添加了"引用标准"选项：
```vue
<el-option label="引用标准" value="reference" />
```

## 功能特点

### 1. 标准化服务要求
- 提供政府采购服务类标准库
- 确保服务要求的规范性和专业性
- 减少手动输入的工作量

### 2. 智能分类识别
- 自动识别当前编辑的标的类型
- 根据标的类型选择对应的标准库
- 智能应用到对应的要求列表

### 3. 灵活配置
- 引用后可以进一步编辑和调整
- 支持设置评审因素和实质性响应
- 可以删除不需要的引用标准

### 4. 一致的用户体验
- 与标的1的技术要求引用功能保持一致
- 相同的操作界面和流程
- 统一的标准选择和应用机制

## 使用场景

### 1. 物业管理服务
- 引用保洁服务标准
- 引用安保服务要求
- 引用设施维护标准

### 2. 专业服务采购
- 引用人员资质要求
- 引用服务质量标准
- 引用响应时间要求

### 3. 综合服务配置
- 批量引用多个服务标准
- 组合不同类型的服务要求
- 建立完整的服务要求体系

## 优势分析

### 1. 提高效率
- 减少重复输入工作
- 快速建立标准化要求
- 提高配置准确性

### 2. 确保规范
- 使用政府采购标准要求
- 避免要求描述不规范
- 提高采购文件质量

### 3. 便于管理
- 统一的标准库管理
- 版本化的标准维护
- 可追溯的引用记录

### 4. 扩展性强
- 支持添加新的服务标准
- 可扩展标准分类
- 便于后续功能增强

## 后续扩展建议

### 1. 标准库完善
- 增加更多服务类标准
- 细化服务分类
- 添加行业特定标准

### 2. 智能推荐
- 根据服务类型推荐标准
- 基于历史配置推荐
- 智能匹配相关标准

### 3. 标准管理
- 标准版本管理
- 标准更新通知
- 标准使用统计

## 总结

标的3的引用需求标准功能已成功实现，为服务类采购项目提供了标准化的服务要求配置能力。功能完整、操作便捷，与现有系统完美集成，为用户提供了高效的服务要求配置工具。
