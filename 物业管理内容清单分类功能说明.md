# 物业管理内容清单分类功能说明

## 功能概述

将物业管理内容清单的维护弹窗重新设计，分成两个独立的标签页：
1. **物业管理服务内容清单** - 管理物业服务提供方需要提供的服务内容
2. **采购人内容提供清单** - 管理采购人需要提供的配合内容和资源

这种分类设计更好地区分了双方的职责，使得物业服务合同的内容更加清晰和专业。

## 界面设计

### 弹窗整体结构
```
┌─────────────────────────────────────────────────────────┐
│ 物业管理内容清单                                         │
├─────────────────────────────────────────────────────────┤
│ [物业管理服务内容清单] [采购人内容提供清单]              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 当前标签页内容                                           │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                                    [取消] [保存]        │
└─────────────────────────────────────────────────────────┘
```

### 弹窗规格
- **弹窗宽度**：900px（比原来的800px更宽，适应双标签页布局）
- **弹窗标题**：物业管理内容清单
- **默认标签页**：物业管理服务内容清单

## 标签页1：物业管理服务内容清单

### 功能说明
管理物业服务提供方需要提供的具体服务内容，采用三级指标体系：
- **一级指标**：服务大类
- **二级指标**：服务子类
- **指标项明细**：具体服务标准和要求

### 表格结构
| 列名 | 宽度 | 类型 | 说明 |
|------|------|------|------|
| 序号 | 自动 | 自动编号 | 显示行号 |
| 一级指标 | 120px | 文本输入 | 服务大类名称 |
| 二级指标 | 120px | 文本输入 | 服务子类名称 |
| 指标项明细 | 200px+ | 文本输入 | 具体服务标准和要求 |
| 操作 | 80px | 删除按钮 | 删除当前行 |

### 预置数据
系统预置了3项基础服务内容：

1. **保洁服务**
   - 一级指标：保洁服务
   - 二级指标：日常清洁
   - 指标项明细：每日清洁、垃圾清理、环境维护，符合物业管理标准

2. **安保服务**
   - 一级指标：安保服务
   - 二级指标：安全管理
   - 指标项明细：门禁管理、巡逻检查、监控维护，24小时值守

3. **设施维修**
   - 一级指标：设施维修
   - 二级指标：维修保养
   - 指标项明细：水电维修、设备保养、应急维修，及时响应

### 操作功能
- **新增服务内容**：点击"新增服务内容"按钮添加新行
- **编辑服务内容**：直接在表格中修改相应字段
- **删除服务内容**：点击行末的"删除"按钮移除内容

## 标签页2：采购人内容提供清单

### 功能说明
管理采购人需要提供的配合内容、资源和支持，确保物业服务能够顺利开展。

### 表格结构
| 列名 | 宽度 | 类型 | 说明 |
|------|------|------|------|
| 序号 | 自动 | 自动编号 | 显示行号 |
| 内容分类 | 120px | 下拉选择 | 提供内容的分类 |
| 提供内容 | 150px | 文本输入 | 具体提供的内容名称 |
| 详细说明 | 200px+ | 文本输入 | 详细说明提供内容的具体要求 |
| 提供时间 | 120px | 下拉选择 | 提供内容的时间要求 |
| 备注 | 150px+ | 文本输入 | 备注信息 |
| 操作 | 80px | 删除按钮 | 删除当前行 |

### 内容分类选项
- **场地提供**：办公场所、存储空间等
- **设备提供**：工具设备、办公设备等
- **人员配合**：联络人员、协调人员等
- **资料提供**：图纸资料、技术文档等
- **其他配合**：其他特殊配合事项

### 提供时间选项
- **服务开始前**：在物业服务正式开始前提供
- **服务期间**：在物业服务进行期间持续提供
- **按需提供**：根据实际需要随时提供
- **一次性提供**：一次性提供，无需重复

### 预置数据
系统预置了3项基础提供内容：

1. **办公场所**
   - 内容分类：场地提供
   - 提供内容：办公场所
   - 详细说明：提供物业管理人员办公场所，面积不少于20平方米
   - 提供时间：服务开始前
   - 备注：包含基本办公设施

2. **清洁工具**
   - 内容分类：设备提供
   - 提供内容：清洁工具
   - 详细说明：提供基础清洁工具和设备存放空间
   - 提供时间：服务开始前
   - 备注：定期更新维护

3. **联络人员**
   - 内容分类：人员配合
   - 提供内容：联络人员
   - 详细说明：指定专门联络人员，负责日常沟通协调
   - 提供时间：服务期间
   - 备注：保持24小时联系畅通

### 操作功能
- **新增提供内容**：点击"新增提供内容"按钮添加新行
- **编辑提供内容**：直接在表格中修改相应字段
- **删除提供内容**：点击行末的"删除"按钮移除内容

## 技术实现

### 1. 数据结构

#### 标签页状态管理
```javascript
const activeManagementTab = ref('service') // 默认显示服务内容清单
```

#### 物业管理服务内容清单数据
```javascript
const serviceContentList = ref([
  {
    id: 1,
    name: '保洁服务',        // 一级指标
    description: '日常清洁', // 二级指标
    standard: '每日清洁、垃圾清理、环境维护，符合物业管理标准' // 指标项明细
  }
])
```

#### 采购人内容提供清单数据
```javascript
const procurementContentList = ref([
  {
    id: 1,
    category: '场地提供',    // 内容分类
    name: '办公场所',        // 提供内容
    description: '提供物业管理人员办公场所，面积不少于20平方米', // 详细说明
    timing: '服务开始前',    // 提供时间
    remark: '包含基本办公设施' // 备注
  }
])
```

### 2. 核心方法

#### 服务内容清单相关方法
```javascript
// 新增服务内容
const addServiceItem = () => {
  const newId = Math.max(...serviceContentList.value.map(item => item.id)) + 1
  serviceContentList.value.push({
    id: newId,
    name: '',
    description: '',
    standard: ''
  })
}

// 删除服务内容
const removeServiceItem = (index: number) => {
  serviceContentList.value.splice(index, 1)
}
```

#### 采购人内容提供清单相关方法
```javascript
// 新增提供内容
const addProcurementItem = () => {
  const newId = Math.max(...procurementContentList.value.map(item => item.id)) + 1
  procurementContentList.value.push({
    id: newId,
    category: '',
    name: '',
    description: '',
    timing: '',
    remark: ''
  })
}

// 删除提供内容
const removeProcurementItem = (index: number) => {
  procurementContentList.value.splice(index, 1)
}
```

### 3. 状态显示

#### 计算总数量
```javascript
const totalManagementItems = computed(() => {
  return serviceContentList.value.length + procurementContentList.value.length
})
```

#### 状态显示逻辑
```vue
<span v-if="totalManagementItems > 0" class="setting-status">
  已配置 {{ totalManagementItems }} 项内容（服务内容：{{ serviceContentList.length }}项，提供内容：{{ procurementContentList.length }}项）
</span>
```

## 界面样式

### 标签页样式
- 标签页头部间距优化
- 标签页内容区域无额外内边距
- 表格最大高度350px，支持滚动

### 表格样式
- 统一的小尺寸表单控件
- 合理的列宽分配
- 清晰的操作按钮

### 响应式设计
- 弹窗宽度900px，适应更多内容
- 表格支持水平滚动
- 移动端友好的交互设计

## 业务价值

### 1. 职责清晰
- **服务方职责**：明确物业服务提供方需要提供的具体服务内容
- **采购方职责**：明确采购人需要提供的配合和支持
- **避免纠纷**：清晰的职责划分减少后续执行中的争议

### 2. 标准化管理
- **服务标准**：统一的服务内容描述和标准要求
- **配合标准**：规范化的配合内容和时间要求
- **质量保证**：详细的标准有助于服务质量控制

### 3. 合同完整性
- **内容完整**：涵盖服务和配合的各个方面
- **条款清晰**：为合同条款提供详细的技术支撑
- **执行明确**：为后续执行提供明确的操作指南

## 使用流程

### 1. 配置服务内容清单
1. 点击"设置"按钮打开弹窗
2. 在"物业管理服务内容清单"标签页中
3. 查看预置的服务内容
4. 根据需要新增、编辑或删除服务内容
5. 填写一级指标、二级指标和指标项明细

### 2. 配置提供内容清单
1. 切换到"采购人内容提供清单"标签页
2. 查看预置的提供内容
3. 根据需要新增、编辑或删除提供内容
4. 选择内容分类和提供时间
5. 填写详细说明和备注信息

### 3. 保存配置
1. 完成两个标签页的配置
2. 点击"保存"按钮确认所有配置
3. 系统显示保存成功提示
4. 弹窗关闭，状态更新

## 扩展功能

### 已实现
- ✅ 双标签页分类管理
- ✅ 服务内容三级指标体系
- ✅ 采购人配合内容管理
- ✅ 预置默认数据
- ✅ 完整的增删改功能

### 待扩展
- 🔄 服务内容模板库
- 🔄 配合内容模板库
- 🔄 导入导出功能
- 🔄 服务标准参考库
- 🔄 配合内容检查清单

## 总结

通过将物业管理内容清单分成两个专业的标签页，实现了：

- 🎯 **职责明确**：清晰区分服务方和采购方的职责
- 🔧 **管理专业**：采用标准化的指标体系和分类管理
- 🎨 **界面友好**：标签页设计直观，操作便捷
- 📊 **数据完整**：涵盖物业服务的各个方面
- 🚀 **业务价值**：提升物业服务采购的专业性和规范性

这种设计使得物业服务类项目的需求配置更加专业和完整，有助于提高采购质量和后续执行效果。
