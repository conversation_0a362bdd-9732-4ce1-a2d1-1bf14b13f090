# 标的2政策和商务要求配置功能说明

## 功能概述

参照标的1的落实政府采购政策和商务要求配置功能，为标的2（项目采购-工程类）配置了相应的政策和商务要求管理功能，支持工程类项目的政策落实和商务条件配置。

## 功能特点

### 1. 完整的配置状态管理
- **配置状态跟踪**：实时显示每个要求类型的配置状态
- **数据统计**：显示已配置的数据条数
- **状态切换**：支持已配置和未配置状态的动态切换

### 2. 统一的编辑界面
- **共享编辑器**：标的1和标的2共享同一套政策和商务要求编辑界面
- **智能识别**：系统自动识别当前编辑的标的，保存到对应的配置中
- **一致体验**：保持与标的1完全一致的操作体验

### 3. 工程类项目适配
- **政策适用性**：政府采购政策同样适用于工程类项目
- **商务要求**：工程项目的商务条件和合同要求
- **技术要求**：工程技术规范和质量标准

## 界面设计

### 标的详细要求卡片
```
┌─────────────────────────────────────┐
│ 落实政府采购政策                     │
├─────────────────────────────────────┤
│ ✓ 已配置3条数据    [编辑配置]        │
│                                     │
│ 或                                  │
│                                     │
│ ? 暂未配置        [点击配置]         │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 商务要求                            │
├─────────────────────────────────────┤
│ ✓ 已配置5条数据    [编辑配置]        │
│                                     │
│ 或                                  │
│                                     │
│ ? 暂未配置        [点击配置]         │
└─────────────────────────────────────┘
```

### 状态显示
- **已配置状态**：绿色勾选图标 + 数据条数 + 编辑配置链接
- **未配置状态**：问号图标 + "暂未配置" + 点击配置按钮

## 技术实现

### 1. 数据结构
```javascript
// 标的2要求配置状态
const target2Requirements = ref({
  policy: {
    configured: false,    // 政策配置状态
    count: 0             // 已配置条数
  },
  technical: {
    configured: false,    // 技术要求配置状态
    count: 0
  },
  business: {
    configured: false,    // 商务要求配置状态
    count: 0
  }
})
```

### 2. 当前编辑标的跟踪
```javascript
// 当前编辑的标的（用于区分标的1和标的2）
const currentEditingTarget = ref('target1')
```

### 3. 核心方法

#### 政策配置方法
```javascript
// 编辑标的2政策
const editTarget2Policy = () => {
  policyEditDrawerVisible.value = true
  currentEditingTarget.value = 'target2'
}

// 配置标的2政策
const configureTarget2Policy = () => {
  ElMessage.info('配置标的2政府采购政策')
  target2Requirements.value.policy.configured = true
  target2Requirements.value.policy.count = 1
}
```

#### 商务要求配置方法
```javascript
// 编辑标的2商务要求
const editTarget2Business = () => {
  businessEditDrawerVisible.value = true
  currentEditingTarget.value = 'target2'
}

// 配置标的2商务要求
const configureTarget2Business = () => {
  ElMessage.info('配置标的2商务要求')
  target2Requirements.value.business.configured = true
  target2Requirements.value.business.count = 3
}
```

### 4. 智能保存机制
```javascript
// 保存政策配置（智能识别当前标的）
const savePolicyConfig = () => {
  // 根据当前编辑的标的更新配置状态
  if (currentEditingTarget.value === 'target1') {
    target1Requirements.value.policy.count = policyList.value.length
    target1Requirements.value.policy.configured = true
  } else if (currentEditingTarget.value === 'target2') {
    target2Requirements.value.policy.count = policyList.value.length
    target2Requirements.value.policy.configured = true
  }
  
  const targetName = currentEditingTarget.value === 'target1' ? '标的1' : '标的2'
  ElMessage.success(`${targetName}政策配置保存成功`)
}
```

## 功能流程

### 1. 政策配置流程
```
点击配置按钮 → 打开政策编辑抽屉 → 设置当前编辑标的 → 
配置政策内容 → 保存配置 → 更新对应标的状态 → 显示成功提示
```

### 2. 商务要求配置流程
```
点击配置按钮 → 打开商务要求编辑抽屉 → 设置当前编辑标的 → 
配置商务要求 → 保存配置 → 更新对应标的状态 → 显示成功提示
```

### 3. 编辑已有配置流程
```
点击编辑配置 → 打开对应编辑抽屉 → 设置当前编辑标的 → 
修改配置内容 → 保存更新 → 更新状态和数量 → 显示成功提示
```

## 配置内容

### 1. 落实政府采购政策
适用于工程类项目的政府采购政策：

#### 政策类型
- **节能产品政府采购政策**：适用于工程中的节能设备
- **环境标志产品政府采购政策**：适用于环保材料和设备
- **信息安全产品政府采购政策**：适用于工程信息化系统
- **小微企业扶持政策**：适用于工程分包和材料采购
- **残疾人福利性单位政策**：适用于特定工程服务
- **监狱企业产品政策**：适用于特定工程材料

#### 政策要求示例
- 优先采购节能环保材料和设备
- 支持小微企业参与工程分包
- 落实信息安全产品认证要求
- 执行绿色建筑标准和要求

### 2. 商务要求
工程类项目的商务条件和合同要求：

#### 商务要求类型
- **资质要求**：施工资质、设计资质等
- **业绩要求**：类似工程业绩、合同金额等
- **人员要求**：项目经理、技术负责人等
- **设备要求**：施工设备、检测设备等
- **财务要求**：注册资金、财务状况等
- **质量要求**：质量管理体系、质量标准等
- **安全要求**：安全生产许可、安全管理等
- **进度要求**：工期要求、里程碑节点等
- **保险要求**：工程保险、责任保险等
- **保证金要求**：履约保证金、质量保证金等

#### 商务要求示例
- 具有建筑工程施工总承包二级及以上资质
- 近三年完成类似规模工程项目不少于2个
- 项目经理具有二级建造师及以上资格
- 具备完善的质量管理体系和安全管理制度
- 提供履约保证金不低于合同金额的10%

## 与标的1的差异

### 相同点
- 使用相同的编辑界面和操作流程
- 相同的数据验证和保存机制
- 一致的状态管理和显示方式

### 差异点
| 特性 | 标的1（设备采购） | 标的2（工程类） |
|------|------------------|----------------|
| 政策适用 | 设备相关政策 | 工程相关政策 |
| 商务要求 | 设备供应商要求 | 工程承包商要求 |
| 技术要求 | 设备技术规格 | 工程技术标准 |
| 配置重点 | 产品质量和性能 | 工程质量和安全 |

## 状态管理

### 初始状态
标的2的所有要求类型初始状态为未配置：
```javascript
target2Requirements = {
  policy: { configured: false, count: 0 },
  technical: { configured: false, count: 0 },
  business: { configured: false, count: 0 }
}
```

### 状态更新
- **配置完成**：`configured: true`，`count: 实际条数`
- **编辑更新**：保持`configured: true`，更新`count`
- **删除配置**：`configured: false`，`count: 0`

## 用户体验

### 1. 操作一致性
- 与标的1完全相同的操作方式
- 相同的界面布局和交互逻辑
- 一致的错误提示和成功反馈

### 2. 智能识别
- 系统自动识别当前操作的标的
- 保存时自动更新对应标的的状态
- 提示信息明确显示操作的标的

### 3. 状态反馈
- 实时显示配置状态和数据条数
- 清晰的已配置/未配置状态区分
- 友好的操作引导和提示

## 扩展功能

### 已实现
- ✅ 完整的政策配置功能
- ✅ 完整的商务要求配置功能
- ✅ 智能的标的识别机制
- ✅ 统一的编辑界面

### 待扩展
- 🔄 工程类专用的技术要求模板
- 🔄 工程进度管理集成
- 🔄 工程质量标准库
- 🔄 工程安全规范库

## 测试要点

### 功能测试
1. 标的2政策配置功能
2. 标的2商务要求配置功能
3. 状态显示和更新正确性
4. 与标的1功能的独立性

### 交互测试
1. 配置按钮点击响应
2. 编辑抽屉正确打开
3. 保存操作正确执行
4. 状态更新及时准确

### 数据测试
1. 配置数据正确保存
2. 状态数据准确更新
3. 标的间数据隔离
4. 数据持久化有效

## 总结

标的2政策和商务要求配置功能的实现特点：

- 🎯 **完全复用**：充分复用标的1的成熟功能和界面
- 🔧 **智能识别**：通过currentEditingTarget实现标的自动识别
- 🎨 **一致体验**：保持与标的1完全一致的用户体验
- 📊 **独立管理**：标的1和标的2的配置数据完全独立
- 🚀 **易于扩展**：为后续标的3、标的4等提供了扩展模式

这个功能为工程类项目提供了专业的政策落实和商务要求管理工具，确保工程采购的合规性和规范性，同时保持了系统的一致性和易用性。
